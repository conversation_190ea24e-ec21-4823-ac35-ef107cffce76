/**
 * Ghost UI Component for MK13 MVP
 * Minimalistic interface that appears only when needed
 */

import React, { useState, useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store/store';
import { setUIVisible, setCurrentMode } from '../store/slices/uiSlice';
import { sendMessage, connectSocket } from '../store/slices/socketSlice';
import ChatInterface from './ChatInterface';
import NotificationOverlay from './NotificationOverlay';
import QuickActions from './QuickActions';
import VoiceInput from './VoiceInput';
import './GhostUI.css';

interface GhostUIProps {
  onClose?: () => void;
}

const GhostUI: React.FC<GhostUIProps> = ({ onClose }) => {
  const dispatch = useDispatch();
  const { isVisible, currentMode, position } = useSelector((state: RootState) => state.ui);
  const { isConnected } = useSelector((state: RootState) => state.socket);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const dragRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Connect to Socket.IO when component mounts
    if (!isConnected) {
      dispatch(connectSocket());
    }
  }, [dispatch, isConnected]);

  useEffect(() => {
    // Global hotkey listener (Ctrl+Shift+M)
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'M') {
        event.preventDefault();
        dispatch(setUIVisible(!isVisible));
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [dispatch, isVisible]);

  const handleClose = () => {
    dispatch(setUIVisible(false));
    if (onClose) onClose();
  };

  const handleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  const handleModeChange = (mode: 'chat' | 'voice' | 'actions') => {
    dispatch(setCurrentMode(mode));
  };

  const handleDragStart = (e: React.MouseEvent) => {
    setIsDragging(true);
    const rect = dragRef.current?.getBoundingClientRect();
    if (rect) {
      const offsetX = e.clientX - rect.left;
      const offsetY = e.clientY - rect.top;

      const handleMouseMove = (e: MouseEvent) => {
        if (dragRef.current) {
          dragRef.current.style.left = `${e.clientX - offsetX}px`;
          dragRef.current.style.top = `${e.clientY - offsetY}px`;
        }
      };

      const handleMouseUp = () => {
        setIsDragging(false);
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <>
      {/* Main Ghost UI Container */}
      <div
        ref={dragRef}
        className={`ghost-ui ${isMinimized ? 'minimized' : ''} ${isDragging ? 'dragging' : ''}`}
        style={{
          position: 'fixed',
          left: position.x,
          top: position.y,
          zIndex: 10000,
        }}
      >
        {/* Header */}
        <div 
          className="ghost-ui-header"
          onMouseDown={handleDragStart}
        >
          <div className="ghost-ui-title">
            <span className="ghost-ui-icon">🤖</span>
            <span>MK13 Assistant</span>
          </div>
          <div className="ghost-ui-controls">
            <button 
              className="ghost-ui-btn minimize"
              onClick={handleMinimize}
              title={isMinimized ? 'Expand' : 'Minimize'}
            >
              {isMinimized ? '□' : '_'}
            </button>
            <button 
              className="ghost-ui-btn close"
              onClick={handleClose}
              title="Close"
            >
              ×
            </button>
          </div>
        </div>

        {/* Content Area */}
        {!isMinimized && (
          <div className="ghost-ui-content">
            {/* Mode Selector */}
            <div className="ghost-ui-modes">
              <button
                className={`mode-btn ${currentMode === 'chat' ? 'active' : ''}`}
                onClick={() => handleModeChange('chat')}
              >
                💬 Chat
              </button>
              <button
                className={`mode-btn ${currentMode === 'voice' ? 'active' : ''}`}
                onClick={() => handleModeChange('voice')}
              >
                🎤 Voice
              </button>
              <button
                className={`mode-btn ${currentMode === 'actions' ? 'active' : ''}`}
                onClick={() => handleModeChange('actions')}
              >
                ⚡ Actions
              </button>
            </div>

            {/* Content based on current mode */}
            <div className="ghost-ui-main">
              {currentMode === 'chat' && <ChatInterface />}
              {currentMode === 'voice' && <VoiceInput />}
              {currentMode === 'actions' && <QuickActions />}
            </div>

            {/* Connection Status */}
            <div className="ghost-ui-status">
              <span className={`status-indicator ${isConnected ? 'connected' : 'disconnected'}`}>
                {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Notification Overlay */}
      <NotificationOverlay />
    </>
  );
};

export default GhostUI;
