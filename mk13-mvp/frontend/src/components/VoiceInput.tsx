/**
 * Voice Input Component
 * Handles voice recognition and speech-to-text
 */

import React, { useState, useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store/store';
import { sendAIMessage, addMessage } from '../store/slices/chatSlice';

interface SpeechRecognitionEvent extends Event {
  results: SpeechRecognitionResultList;
  resultIndex: number;
}

interface SpeechRecognitionErrorEvent extends Event {
  error: string;
  message: string;
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  start(): void;
  stop(): void;
  abort(): void;
  onstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onresult: ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => any) | null;
  onerror: ((this: SpeechRecognition, ev: SpeechRecognitionErrorEvent) => any) | null;
}

declare global {
  interface Window {
    SpeechRecognition: new () => SpeechRecognition;
    webkitSpeechRecognition: new () => SpeechRecognition;
  }
}

const VoiceInput: React.FC = () => {
  const dispatch = useDispatch();
  const { userId } = useSelector((state: RootState) => state.user);
  const { isLoading } = useSelector((state: RootState) => state.chat);
  
  const [isRecording, setIsRecording] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [isSupported, setIsSupported] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const recognitionRef = useRef<SpeechRecognition | null>(null);

  useEffect(() => {
    // Check if speech recognition is supported
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    
    if (SpeechRecognition) {
      setIsSupported(true);
      
      const recognition = new SpeechRecognition();
      recognition.continuous = false;
      recognition.interimResults = true;
      recognition.lang = 'en-US'; // TODO: Make this configurable
      
      recognition.onstart = () => {
        setIsRecording(true);
        setError(null);
        setTranscript('');
      };
      
      recognition.onend = () => {
        setIsRecording(false);
      };
      
      recognition.onresult = (event: SpeechRecognitionEvent) => {
        let finalTranscript = '';
        let interimTranscript = '';
        
        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i];
          if (result.isFinal) {
            finalTranscript += result[0].transcript;
          } else {
            interimTranscript += result[0].transcript;
          }
        }
        
        setTranscript(finalTranscript || interimTranscript);
        
        // If we have a final result, process it
        if (finalTranscript) {
          handleVoiceMessage(finalTranscript.trim());
        }
      };
      
      recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
        setError(`Speech recognition error: ${event.error}`);
        setIsRecording(false);
      };
      
      recognitionRef.current = recognition;
    } else {
      setIsSupported(false);
      setError('Speech recognition is not supported in this browser');
    }
    
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.abort();
      }
    };
  }, []);

  const handleVoiceMessage = async (message: string) => {
    if (!message.trim()) return;

    // Add user message to chat
    dispatch(addMessage({
      id: Date.now().toString(),
      type: 'user',
      content: message,
      timestamp: new Date().toISOString(),
    }));

    // Send to AI service
    try {
      await dispatch(sendAIMessage({
        userId: userId || 'anonymous',
        message: message,
        contextId: null,
      }));
    } catch (error) {
      console.error('Error sending voice message:', error);
      dispatch(addMessage({
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: 'Sorry, I encountered an error processing your voice message. Please try again.',
        timestamp: new Date().toISOString(),
      }));
    }
  };

  const startRecording = () => {
    if (!recognitionRef.current || isRecording) return;
    
    try {
      recognitionRef.current.start();
    } catch (error) {
      setError('Failed to start voice recognition');
      console.error('Voice recognition error:', error);
    }
  };

  const stopRecording = () => {
    if (!recognitionRef.current || !isRecording) return;
    
    recognitionRef.current.stop();
  };

  const toggleRecording = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  if (!isSupported) {
    return (
      <div className="voice-input">
        <div className="voice-error">
          <span className="error-icon">🚫</span>
          <p>Voice input is not supported in this browser.</p>
          <p>Please use Chrome, Edge, or Safari for voice features.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="voice-input">
      {/* Voice Button */}
      <button
        className={`voice-btn ${isRecording ? 'recording' : ''}`}
        onClick={toggleRecording}
        disabled={isLoading}
        title={isRecording ? 'Stop recording' : 'Start recording'}
      >
        {isRecording ? '🔴' : '🎤'}
      </button>

      {/* Status */}
      <div className="voice-status">
        {isRecording ? (
          <p>🎙️ Listening... Speak now</p>
        ) : (
          <p>Click the microphone to start speaking</p>
        )}
      </div>

      {/* Transcript */}
      {transcript && (
        <div className="voice-transcript">
          <p><strong>Transcript:</strong></p>
          <p>"{transcript}"</p>
        </div>
      )}

      {/* Error */}
      {error && (
        <div className="voice-error">
          <p>⚠️ {error}</p>
        </div>
      )}

      {/* Instructions */}
      <div className="voice-instructions">
        <p>💡 <strong>Tips:</strong></p>
        <ul>
          <li>Speak clearly and at normal pace</li>
          <li>Minimize background noise</li>
          <li>Click stop when finished speaking</li>
        </ul>
      </div>
    </div>
  );
};

export default VoiceInput;
