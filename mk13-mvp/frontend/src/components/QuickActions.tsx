/**
 * Quick Actions Component
 * Provides quick access to common AI assistant functions
 */

import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store/store';
import { sendAIMessage, addMessage } from '../store/slices/chatSlice';

interface QuickAction {
  id: string;
  title: string;
  icon: string;
  description: string;
  prompt: string;
  category: 'productivity' | 'communication' | 'analysis' | 'creative';
}

const quickActions: QuickAction[] = [
  {
    id: 'summarize-emails',
    title: 'Summarize Emails',
    icon: '📧',
    description: 'Get a summary of recent emails',
    prompt: 'Please summarize my recent unread emails and highlight any important items that need my attention.',
    category: 'communication'
  },
  {
    id: 'schedule-overview',
    title: 'Today\'s Schedule',
    icon: '📅',
    description: 'View today\'s calendar',
    prompt: 'Show me my schedule for today and any upcoming meetings or events.',
    category: 'productivity'
  },
  {
    id: 'task-suggestions',
    title: 'Task Suggestions',
    icon: '✅',
    description: 'Get productivity suggestions',
    prompt: 'Based on my current context and recent activity, suggest 3-5 productive tasks I should focus on.',
    category: 'productivity'
  },
  {
    id: 'draft-email',
    title: 'Draft Email',
    icon: '✍️',
    description: 'Help compose an email',
    prompt: 'Help me draft a professional email. What would you like the email to be about?',
    category: 'communication'
  },
  {
    id: 'analyze-documents',
    title: 'Analyze Documents',
    icon: '📄',
    description: 'Analyze recent documents',
    prompt: 'Analyze my recent documents and files, and provide insights or summaries of key information.',
    category: 'analysis'
  },
  {
    id: 'brainstorm-ideas',
    title: 'Brainstorm Ideas',
    icon: '💡',
    description: 'Generate creative ideas',
    prompt: 'Let\'s brainstorm some creative ideas. What topic or project would you like to explore?',
    category: 'creative'
  },
  {
    id: 'meeting-prep',
    title: 'Meeting Prep',
    icon: '🎯',
    description: 'Prepare for meetings',
    prompt: 'Help me prepare for my upcoming meetings. Review my calendar and suggest preparation steps.',
    category: 'productivity'
  },
  {
    id: 'quick-research',
    title: 'Quick Research',
    icon: '🔍',
    description: 'Research a topic',
    prompt: 'I need to research a topic quickly. What would you like me to help you research?',
    category: 'analysis'
  }
];

const QuickActions: React.FC = () => {
  const dispatch = useDispatch();
  const { userId } = useSelector((state: RootState) => state.user);
  const { isLoading } = useSelector((state: RootState) => state.chat);

  const handleActionClick = async (action: QuickAction) => {
    if (isLoading) return;

    // Add user message indicating the action
    dispatch(addMessage({
      id: Date.now().toString(),
      type: 'user',
      content: `🎯 ${action.title}`,
      timestamp: new Date().toISOString(),
    }));

    // Send the action prompt to AI
    try {
      await dispatch(sendAIMessage({
        userId: userId || 'anonymous',
        message: action.prompt,
        contextId: null,
      }));
    } catch (error) {
      console.error('Error executing quick action:', error);
      dispatch(addMessage({
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: `Sorry, I encountered an error executing "${action.title}". Please try again.`,
        timestamp: new Date().toISOString(),
      }));
    }
  };

  const getCategoryActions = (category: string) => {
    return quickActions.filter(action => action.category === category);
  };

  const categories = [
    { id: 'productivity', name: 'Productivity', color: '#007AFF' },
    { id: 'communication', name: 'Communication', color: '#34C759' },
    { id: 'analysis', name: 'Analysis', color: '#FF9500' },
    { id: 'creative', name: 'Creative', color: '#AF52DE' }
  ];

  return (
    <div className="quick-actions">
      {categories.map(category => {
        const actions = getCategoryActions(category.id);
        if (actions.length === 0) return null;

        return (
          <div key={category.id} className="action-category">
            <div 
              className="category-header"
              style={{ borderLeftColor: category.color }}
            >
              <h4>{category.name}</h4>
            </div>
            <div className="action-grid">
              {actions.map(action => (
                <button
                  key={action.id}
                  className="action-btn"
                  onClick={() => handleActionClick(action)}
                  disabled={isLoading}
                  title={action.description}
                >
                  <span className="action-icon">{action.icon}</span>
                  <span className="action-title">{action.title}</span>
                </button>
              ))}
            </div>
          </div>
        );
      })}

      {/* Custom Action Input */}
      <div className="custom-action">
        <div className="category-header">
          <h4>Custom Action</h4>
        </div>
        <div className="custom-action-input">
          <input
            type="text"
            placeholder="Type a custom request..."
            className="custom-input"
            onKeyPress={(e) => {
              if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                const customPrompt = e.currentTarget.value.trim();
                handleActionClick({
                  id: 'custom',
                  title: 'Custom Request',
                  icon: '⚡',
                  description: 'Custom user request',
                  prompt: customPrompt,
                  category: 'productivity'
                });
                e.currentTarget.value = '';
              }
            }}
            disabled={isLoading}
          />
          <span className="input-hint">Press Enter to execute</span>
        </div>
      </div>
    </div>
  );
};

export default QuickActions;
