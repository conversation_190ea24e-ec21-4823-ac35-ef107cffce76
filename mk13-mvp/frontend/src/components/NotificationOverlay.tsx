/**
 * Notification Overlay Component
 * Displays real-time notifications and alerts
 */

import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store/store';
import { removeNotification } from '../store/slices/notificationSlice';

interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'ai_suggestion';
  title: string;
  message: string;
  timestamp: string;
  autoClose?: boolean;
  duration?: number;
  actions?: Array<{
    label: string;
    action: () => void;
    style?: 'primary' | 'secondary';
  }>;
}

const NotificationOverlay: React.FC = () => {
  const dispatch = useDispatch();
  const { notifications } = useSelector((state: RootState) => state.notifications);
  const [visibleNotifications, setVisibleNotifications] = useState<string[]>([]);

  useEffect(() => {
    // Auto-close notifications
    notifications.forEach(notification => {
      if (notification.autoClose !== false) {
        const duration = notification.duration || 5000;
        const timer = setTimeout(() => {
          handleCloseNotification(notification.id);
        }, duration);

        return () => clearTimeout(timer);
      }
    });
  }, [notifications]);

  useEffect(() => {
    // Animate in new notifications
    const newNotifications = notifications.filter(
      n => !visibleNotifications.includes(n.id)
    );

    newNotifications.forEach(notification => {
      setTimeout(() => {
        setVisibleNotifications(prev => [...prev, notification.id]);
      }, 100);
    });
  }, [notifications, visibleNotifications]);

  const handleCloseNotification = (id: string) => {
    setVisibleNotifications(prev => prev.filter(nId => nId !== id));
    setTimeout(() => {
      dispatch(removeNotification(id));
    }, 300); // Wait for animation
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return '✅';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      case 'ai_suggestion': return '🤖';
      default: return 'ℹ️';
    }
  };

  const getNotificationClass = (type: string) => {
    return `notification notification-${type}`;
  };

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className="notification-overlay">
      {notifications.map(notification => (
        <div
          key={notification.id}
          className={`${getNotificationClass(notification.type)} ${
            visibleNotifications.includes(notification.id) ? 'visible' : ''
          }`}
        >
          <div className="notification-header">
            <span className="notification-icon">
              {getNotificationIcon(notification.type)}
            </span>
            <span className="notification-title">{notification.title}</span>
            <button
              className="notification-close"
              onClick={() => handleCloseNotification(notification.id)}
            >
              ×
            </button>
          </div>
          
          <div className="notification-content">
            <p className="notification-message">{notification.message}</p>
            
            {notification.actions && notification.actions.length > 0 && (
              <div className="notification-actions">
                {notification.actions.map((action, index) => (
                  <button
                    key={index}
                    className={`notification-action-btn ${action.style || 'secondary'}`}
                    onClick={() => {
                      action.action();
                      handleCloseNotification(notification.id);
                    }}
                  >
                    {action.label}
                  </button>
                ))}
              </div>
            )}
          </div>
          
          <div className="notification-timestamp">
            {new Date(notification.timestamp).toLocaleTimeString()}
          </div>
        </div>
      ))}
    </div>
  );
};

export default NotificationOverlay;
