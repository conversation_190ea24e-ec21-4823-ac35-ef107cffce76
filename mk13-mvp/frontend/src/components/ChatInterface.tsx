/**
 * Chat Interface Component
 * Handles text-based AI interactions
 */

import React, { useState, useRef, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store/store';
import { sendAIMessage, addMessage } from '../store/slices/chatSlice';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: string;
  suggestions?: Array<{
    title: string;
    description: string;
  }>;
}

const ChatInterface: React.FC = () => {
  const dispatch = useDispatch();
  const { messages, isLoading } = useSelector((state: RootState) => state.chat);
  const { userId } = useSelector((state: RootState) => state.user);
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // Auto-scroll to bottom when new messages arrive
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  useEffect(() => {
    // Focus input when component mounts
    inputRef.current?.focus();
  }, []);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue.trim(),
      timestamp: new Date().toISOString(),
    };

    // Add user message to chat
    dispatch(addMessage(userMessage));

    // Clear input
    const messageToSend = inputValue.trim();
    setInputValue('');

    // Send to AI service
    try {
      await dispatch(sendAIMessage({
        userId: userId || 'anonymous',
        message: messageToSend,
        contextId: null, // TODO: Implement context management
      }));
    } catch (error) {
      console.error('Error sending message:', error);
      // Add error message
      dispatch(addMessage({
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: 'Sorry, I encountered an error processing your message. Please try again.',
        timestamp: new Date().toISOString(),
      }));
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleSuggestionClick = (suggestion: { title: string; description: string }) => {
    setInputValue(suggestion.title);
    inputRef.current?.focus();
  };

  return (
    <div className="chat-interface">
      {/* Messages Area */}
      <div className="chat-messages">
        {messages.length === 0 ? (
          <div className="chat-welcome">
            <p>👋 Hi! I'm your MK13 AI assistant.</p>
            <p>How can I help you today?</p>
          </div>
        ) : (
          messages.map((message) => (
            <div key={message.id} className={`chat-message ${message.type}`}>
              <div className="message-content">{message.content}</div>
              {message.suggestions && message.suggestions.length > 0 && (
                <div className="message-suggestions">
                  {message.suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      className="suggestion-btn"
                      onClick={() => handleSuggestionClick(suggestion)}
                      title={suggestion.description}
                    >
                      {suggestion.title}
                    </button>
                  ))}
                </div>
              )}
            </div>
          ))
        )}
        {isLoading && (
          <div className="chat-message assistant">
            <div className="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="chat-input-container">
        <input
          ref={inputRef}
          type="text"
          className="chat-input"
          placeholder="Type your message..."
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress}
          disabled={isLoading}
        />
        <button
          className="send-btn"
          onClick={handleSendMessage}
          disabled={!inputValue.trim() || isLoading}
        >
          {isLoading ? '⏳' : '📤'}
        </button>
      </div>
    </div>
  );
};

export default ChatInterface;
