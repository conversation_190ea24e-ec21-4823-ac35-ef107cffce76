/**
 * Context State Management Slice
 * Manages user contexts, work environments, and context switching
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface Context {
  id: string;
  name: string;
  state: {
    activeApplications?: string[];
    currentProject?: string;
    workingDirectory?: string;
    openFiles?: string[];
    browserTabs?: Array<{
      title: string;
      url: string;
    }>;
    conversation?: Array<{
      role: 'user' | 'assistant';
      content: string;
      timestamp: string;
    }>;
    lastInteraction?: string;
  };
  metadata: {
    createdAt: string;
    updatedAt: string;
    tags?: string[];
    category?: string;
    priority?: 'low' | 'medium' | 'high';
  };
  userId: string;
}

export interface ContextState {
  contexts: Context[];
  currentContext: Context | null;
  isLoading: boolean;
  error: string | null;
  autoSaveEnabled: boolean;
  contextSwitchHistory: string[]; // Context IDs
}

const initialState: ContextState = {
  contexts: [],
  currentContext: null,
  isLoading: false,
  error: null,
  autoSaveEnabled: true,
  contextSwitchHistory: [],
};

// Async thunk for fetching user contexts
export const fetchUserContexts = createAsyncThunk(
  'context/fetchUserContexts',
  async (userId: string) => {
    const response = await fetch(`http://localhost:8000/api/users/${userId}/contexts`);
    
    if (!response.ok) {
      throw new Error('Failed to fetch contexts');
    }
    
    const data = await response.json();
    return data.contexts;
  }
);

// Async thunk for creating a new context
export const createContext = createAsyncThunk(
  'context/createContext',
  async (payload: {
    userId: string;
    name: string;
    state: Context['state'];
    metadata?: Partial<Context['metadata']>;
  }) => {
    const response = await fetch('http://localhost:8000/api/contexts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: payload.userId,
        name: payload.name,
        state: payload.state,
        metadata: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          ...payload.metadata,
        },
      }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to create context');
    }
    
    const data = await response.json();
    return data.context;
  }
);

// Async thunk for updating a context
export const updateContext = createAsyncThunk(
  'context/updateContext',
  async (payload: {
    contextId: string;
    updates: Partial<Context>;
  }) => {
    const response = await fetch(`http://localhost:8000/api/contexts/${payload.contextId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...payload.updates,
        metadata: {
          ...payload.updates.metadata,
          updatedAt: new Date().toISOString(),
        },
      }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to update context');
    }
    
    const data = await response.json();
    return data.context;
  }
);

const contextSlice = createSlice({
  name: 'context',
  initialState,
  reducers: {
    setCurrentContext: (state, action: PayloadAction<Context | null>) => {
      if (action.payload && state.currentContext?.id !== action.payload.id) {
        // Add to history
        if (action.payload.id) {
          state.contextSwitchHistory.unshift(action.payload.id);
          // Keep only last 10 contexts in history
          state.contextSwitchHistory = state.contextSwitchHistory.slice(0, 10);
        }
      }
      state.currentContext = action.payload;
    },
    addContext: (state, action: PayloadAction<Context>) => {
      state.contexts.push(action.payload);
    },
    updateLocalContext: (state, action: PayloadAction<{ id: string; updates: Partial<Context> }>) => {
      const contextIndex = state.contexts.findIndex(ctx => ctx.id === action.payload.id);
      if (contextIndex !== -1) {
        state.contexts[contextIndex] = {
          ...state.contexts[contextIndex],
          ...action.payload.updates,
          metadata: {
            ...state.contexts[contextIndex].metadata,
            ...action.payload.updates.metadata,
            updatedAt: new Date().toISOString(),
          },
        };
        
        // Update current context if it's the same
        if (state.currentContext?.id === action.payload.id) {
          state.currentContext = state.contexts[contextIndex];
        }
      }
    },
    removeContext: (state, action: PayloadAction<string>) => {
      state.contexts = state.contexts.filter(ctx => ctx.id !== action.payload);
      
      // Clear current context if it was removed
      if (state.currentContext?.id === action.payload) {
        state.currentContext = null;
      }
      
      // Remove from history
      state.contextSwitchHistory = state.contextSwitchHistory.filter(id => id !== action.payload);
    },
    updateCurrentContextState: (state, action: PayloadAction<Partial<Context['state']>>) => {
      if (state.currentContext) {
        state.currentContext.state = {
          ...state.currentContext.state,
          ...action.payload,
          lastInteraction: new Date().toISOString(),
        };
        
        // Update in contexts array
        const contextIndex = state.contexts.findIndex(ctx => ctx.id === state.currentContext?.id);
        if (contextIndex !== -1) {
          state.contexts[contextIndex] = state.currentContext;
        }
      }
    },
    addConversationToCurrentContext: (state, action: PayloadAction<{
      role: 'user' | 'assistant';
      content: string;
    }>) => {
      if (state.currentContext) {
        const conversation = state.currentContext.state.conversation || [];
        conversation.push({
          ...action.payload,
          timestamp: new Date().toISOString(),
        });
        
        // Keep only last 50 messages
        state.currentContext.state.conversation = conversation.slice(-50);
        state.currentContext.state.lastInteraction = new Date().toISOString();
        
        // Update in contexts array
        const contextIndex = state.contexts.findIndex(ctx => ctx.id === state.currentContext?.id);
        if (contextIndex !== -1) {
          state.contexts[contextIndex] = state.currentContext;
        }
      }
    },
    setAutoSave: (state, action: PayloadAction<boolean>) => {
      state.autoSaveEnabled = action.payload;
    },
    clearContextHistory: (state) => {
      state.contextSwitchHistory = [];
    },
    clearError: (state) => {
      state.error = null;
    },
    // Quick context creation for common scenarios
    createQuickContext: (state, action: PayloadAction<{
      name: string;
      type: 'work' | 'personal' | 'project' | 'meeting';
      userId: string;
    }>) => {
      const quickContext: Context = {
        id: `quick_${Date.now()}`,
        name: action.payload.name,
        state: {
          lastInteraction: new Date().toISOString(),
        },
        metadata: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          category: action.payload.type,
          priority: 'medium',
        },
        userId: action.payload.userId,
      };
      
      state.contexts.push(quickContext);
      state.currentContext = quickContext;
      
      // Add to history
      state.contextSwitchHistory.unshift(quickContext.id);
      state.contextSwitchHistory = state.contextSwitchHistory.slice(0, 10);
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch User Contexts
      .addCase(fetchUserContexts.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchUserContexts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.contexts = action.payload;
      })
      .addCase(fetchUserContexts.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch contexts';
      })
      // Create Context
      .addCase(createContext.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createContext.fulfilled, (state, action) => {
        state.isLoading = false;
        state.contexts.push(action.payload);
      })
      .addCase(createContext.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to create context';
      })
      // Update Context
      .addCase(updateContext.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateContext.fulfilled, (state, action) => {
        state.isLoading = false;
        const contextIndex = state.contexts.findIndex(ctx => ctx.id === action.payload.id);
        if (contextIndex !== -1) {
          state.contexts[contextIndex] = action.payload;
          
          // Update current context if it's the same
          if (state.currentContext?.id === action.payload.id) {
            state.currentContext = action.payload;
          }
        }
      })
      .addCase(updateContext.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to update context';
      });
  },
});

export const {
  setCurrentContext,
  addContext,
  updateLocalContext,
  removeContext,
  updateCurrentContextState,
  addConversationToCurrentContext,
  setAutoSave,
  clearContextHistory,
  clearError,
  createQuickContext,
} = contextSlice.actions;

export default contextSlice.reducer;
