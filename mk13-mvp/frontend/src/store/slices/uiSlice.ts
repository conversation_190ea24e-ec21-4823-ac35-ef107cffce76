/**
 * UI State Management Slice
 * Manages Ghost UI visibility, position, and mode
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface UIState {
  isVisible: boolean;
  currentMode: 'chat' | 'voice' | 'actions';
  position: {
    x: number;
    y: number;
  };
  isMinimized: boolean;
  theme: 'dark' | 'light';
  settings: {
    autoHide: boolean;
    hotkey: string;
    opacity: number;
    alwaysOnTop: boolean;
  };
}

const initialState: UIState = {
  isVisible: false,
  currentMode: 'chat',
  position: {
    x: window.innerWidth - 420, // 400px width + 20px margin
    y: 100,
  },
  isMinimized: false,
  theme: 'dark',
  settings: {
    autoHide: false,
    hotkey: 'Ctrl+Shift+M',
    opacity: 0.95,
    alwaysOnTop: true,
  },
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setUIVisible: (state, action: PayloadAction<boolean>) => {
      state.isVisible = action.payload;
    },
    toggleUIVisible: (state) => {
      state.isVisible = !state.isVisible;
    },
    setCurrentMode: (state, action: PayloadAction<'chat' | 'voice' | 'actions'>) => {
      state.currentMode = action.payload;
    },
    setPosition: (state, action: PayloadAction<{ x: number; y: number }>) => {
      state.position = action.payload;
    },
    setMinimized: (state, action: PayloadAction<boolean>) => {
      state.isMinimized = action.payload;
    },
    toggleMinimized: (state) => {
      state.isMinimized = !state.isMinimized;
    },
    setTheme: (state, action: PayloadAction<'dark' | 'light'>) => {
      state.theme = action.payload;
    },
    updateSettings: (state, action: PayloadAction<Partial<UIState['settings']>>) => {
      state.settings = { ...state.settings, ...action.payload };
    },
    resetUI: (state) => {
      return { ...initialState, position: state.position }; // Keep position
    },
  },
});

export const {
  setUIVisible,
  toggleUIVisible,
  setCurrentMode,
  setPosition,
  setMinimized,
  toggleMinimized,
  setTheme,
  updateSettings,
  resetUI,
} = uiSlice.actions;

export default uiSlice.reducer;
