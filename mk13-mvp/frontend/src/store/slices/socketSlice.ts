/**
 * Socket.IO State Management Slice
 * Manages real-time communication with backend
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { io, Socket } from 'socket.io-client';
import { addMessage } from './chatSlice';
import { addNotification } from './notificationSlice';

export interface SocketState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  connectionId: string | null;
  lastPing: number | null;
  reconnectAttempts: number;
  maxReconnectAttempts: number;
}

const initialState: SocketState = {
  isConnected: false,
  isConnecting: false,
  error: null,
  connectionId: null,
  lastPing: null,
  reconnectAttempts: 0,
  maxReconnectAttempts: 5,
};

let socket: Socket | null = null;

// Async thunk for connecting to Socket.IO
export const connectSocket = createAsyncThunk(
  'socket/connect',
  async (_, { dispatch, getState }) => {
    return new Promise<string>((resolve, reject) => {
      try {
        // Disconnect existing socket if any
        if (socket) {
          socket.disconnect();
        }

        // Create new socket connection
        socket = io('http://localhost:8000', {
          transports: ['websocket', 'polling'],
          timeout: 5000,
          reconnection: true,
          reconnectionAttempts: 5,
          reconnectionDelay: 1000,
        });

        // Connection successful
        socket.on('connect', () => {
          console.log('Socket connected:', socket?.id);
          dispatch(socketSlice.actions.setConnected(true));
          dispatch(socketSlice.actions.setConnectionId(socket?.id || null));
          dispatch(socketSlice.actions.setError(null));
          dispatch(socketSlice.actions.resetReconnectAttempts());
          resolve(socket?.id || '');
        });

        // Connection error
        socket.on('connect_error', (error) => {
          console.error('Socket connection error:', error);
          dispatch(socketSlice.actions.setError(error.message));
          dispatch(socketSlice.actions.incrementReconnectAttempts());
          reject(error);
        });

        // Disconnection
        socket.on('disconnect', (reason) => {
          console.log('Socket disconnected:', reason);
          dispatch(socketSlice.actions.setConnected(false));
          dispatch(socketSlice.actions.setConnectionId(null));
          
          if (reason === 'io server disconnect') {
            // Server initiated disconnect, don't reconnect automatically
            dispatch(socketSlice.actions.setError('Server disconnected'));
          }
        });

        // AI response handler
        socket.on('ai_response', (data) => {
          dispatch(addMessage({
            id: Date.now().toString(),
            type: 'assistant',
            content: data.response,
            timestamp: data.timestamp,
            suggestions: data.suggestions,
            metadata: {
              contextId: data.context_id,
            },
          }));
        });

        // Notification handler
        socket.on('notification', (data) => {
          dispatch(addNotification({
            id: Date.now().toString(),
            type: data.type,
            title: data.title || 'Notification',
            message: data.message || '',
            timestamp: data.timestamp,
            autoClose: data.autoClose !== false,
            duration: data.duration || 5000,
          }));
        });

        // AI notification handler
        socket.on('ai_notification', (data) => {
          if (data.type === 'ai_response') {
            dispatch(addNotification({
              id: Date.now().toString(),
              type: 'ai_suggestion',
              title: '🤖 AI Response',
              message: 'New AI response received',
              timestamp: new Date().toISOString(),
              autoClose: true,
              duration: 3000,
            }));
          }
        });

        // Suggestions response handler
        socket.on('suggestions_response', (data) => {
          // Handle suggestions if needed
          console.log('Suggestions received:', data.suggestions);
        });

        // Error handler
        socket.on('error', (data) => {
          dispatch(socketSlice.actions.setError(data.message));
          dispatch(addNotification({
            id: Date.now().toString(),
            type: 'error',
            title: 'Error',
            message: data.message,
            timestamp: new Date().toISOString(),
            autoClose: true,
            duration: 5000,
          }));
        });

        // Connection response handler
        socket.on('connection_response', (data) => {
          console.log('Connection confirmed:', data);
        });

      } catch (error) {
        reject(error);
      }
    });
  }
);

// Async thunk for disconnecting
export const disconnectSocket = createAsyncThunk(
  'socket/disconnect',
  async () => {
    if (socket) {
      socket.disconnect();
      socket = null;
    }
  }
);

// Async thunk for sending messages
export const sendMessage = createAsyncThunk(
  'socket/sendMessage',
  async (payload: {
    event: string;
    data: any;
  }) => {
    if (!socket || !socket.connected) {
      throw new Error('Socket not connected');
    }

    socket.emit(payload.event, payload.data);
  }
);

// Async thunk for joining user room
export const joinUserRoom = createAsyncThunk(
  'socket/joinUserRoom',
  async (userId: string) => {
    if (!socket || !socket.connected) {
      throw new Error('Socket not connected');
    }

    socket.emit('join_user_room', { user_id: userId });
  }
);

const socketSlice = createSlice({
  name: 'socket',
  initialState,
  reducers: {
    setConnected: (state, action: PayloadAction<boolean>) => {
      state.isConnected = action.payload;
      state.isConnecting = false;
    },
    setConnecting: (state, action: PayloadAction<boolean>) => {
      state.isConnecting = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setConnectionId: (state, action: PayloadAction<string | null>) => {
      state.connectionId = action.payload;
    },
    setPing: (state, action: PayloadAction<number>) => {
      state.lastPing = action.payload;
    },
    incrementReconnectAttempts: (state) => {
      state.reconnectAttempts += 1;
    },
    resetReconnectAttempts: (state) => {
      state.reconnectAttempts = 0;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(connectSocket.pending, (state) => {
        state.isConnecting = true;
        state.error = null;
      })
      .addCase(connectSocket.fulfilled, (state, action) => {
        state.isConnecting = false;
        state.isConnected = true;
        state.connectionId = action.payload;
        state.error = null;
      })
      .addCase(connectSocket.rejected, (state, action) => {
        state.isConnecting = false;
        state.isConnected = false;
        state.error = action.error.message || 'Connection failed';
      })
      .addCase(disconnectSocket.fulfilled, (state) => {
        state.isConnected = false;
        state.connectionId = null;
        state.isConnecting = false;
      });
  },
});

export const {
  setConnected,
  setConnecting,
  setError,
  setConnectionId,
  setPing,
  incrementReconnectAttempts,
  resetReconnectAttempts,
  clearError,
} = socketSlice.actions;

export default socketSlice.reducer;

// Export socket instance for direct use if needed
export const getSocket = () => socket;
