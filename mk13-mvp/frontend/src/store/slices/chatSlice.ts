/**
 * Chat State Management Slice
 * Manages chat messages, AI interactions, and conversation history
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface Message {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  suggestions?: Array<{
    title: string;
    description: string;
  }>;
  metadata?: {
    contextId?: string;
    actionId?: string;
    source?: string;
  };
}

export interface ChatState {
  messages: Message[];
  isLoading: boolean;
  error: string | null;
  currentContextId: string | null;
  conversationHistory: Message[];
  suggestions: Array<{
    title: string;
    description: string;
  }>;
}

const initialState: ChatState = {
  messages: [],
  isLoading: false,
  error: null,
  currentContextId: null,
  conversationHistory: [],
  suggestions: [],
};

// Async thunk for sending AI messages
export const sendAIMessage = createAsyncThunk(
  'chat/sendAIMessage',
  async (payload: {
    userId: string;
    message: string;
    contextId?: string | null;
  }) => {
    const response = await fetch('http://localhost:8000/api/ai/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: payload.userId,
        message: payload.message,
        context_id: payload.contextId,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to send message to AI');
    }

    const data = await response.json();
    return data.result;
  }
);

// Async thunk for requesting suggestions
export const requestSuggestions = createAsyncThunk(
  'chat/requestSuggestions',
  async (payload: {
    userId: string;
    contextData: any;
  }) => {
    const response = await fetch('http://localhost:8000/api/ai/suggestions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: payload.userId,
        context_data: payload.contextData,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to get suggestions');
    }

    const data = await response.json();
    return data.suggestions;
  }
);

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    addMessage: (state, action: PayloadAction<Message>) => {
      state.messages.push(action.payload);
      state.conversationHistory.push(action.payload);
      
      // Keep only last 50 messages in history
      if (state.conversationHistory.length > 50) {
        state.conversationHistory = state.conversationHistory.slice(-50);
      }
    },
    removeMessage: (state, action: PayloadAction<string>) => {
      state.messages = state.messages.filter(msg => msg.id !== action.payload);
      state.conversationHistory = state.conversationHistory.filter(msg => msg.id !== action.payload);
    },
    clearMessages: (state) => {
      state.messages = [];
    },
    clearConversationHistory: (state) => {
      state.conversationHistory = [];
    },
    setCurrentContextId: (state, action: PayloadAction<string | null>) => {
      state.currentContextId = action.payload;
    },
    setSuggestions: (state, action: PayloadAction<Array<{ title: string; description: string }>>) => {
      state.suggestions = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    updateMessage: (state, action: PayloadAction<{ id: string; updates: Partial<Message> }>) => {
      const messageIndex = state.messages.findIndex(msg => msg.id === action.payload.id);
      if (messageIndex !== -1) {
        state.messages[messageIndex] = { ...state.messages[messageIndex], ...action.payload.updates };
      }
      
      const historyIndex = state.conversationHistory.findIndex(msg => msg.id === action.payload.id);
      if (historyIndex !== -1) {
        state.conversationHistory[historyIndex] = { 
          ...state.conversationHistory[historyIndex], 
          ...action.payload.updates 
        };
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Send AI Message
      .addCase(sendAIMessage.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(sendAIMessage.fulfilled, (state, action) => {
        state.isLoading = false;
        
        // Add AI response message
        const aiMessage: Message = {
          id: Date.now().toString(),
          type: 'assistant',
          content: action.payload.response,
          timestamp: action.payload.timestamp,
          suggestions: action.payload.suggestions,
          metadata: {
            contextId: action.payload.context_id,
          },
        };
        
        state.messages.push(aiMessage);
        state.conversationHistory.push(aiMessage);
        state.currentContextId = action.payload.context_id;
        state.suggestions = action.payload.suggestions || [];
        
        // Keep only last 50 messages in history
        if (state.conversationHistory.length > 50) {
          state.conversationHistory = state.conversationHistory.slice(-50);
        }
      })
      .addCase(sendAIMessage.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to send message';
      })
      // Request Suggestions
      .addCase(requestSuggestions.pending, (state) => {
        // Don't set loading for suggestions as it's background
      })
      .addCase(requestSuggestions.fulfilled, (state, action) => {
        state.suggestions = action.payload;
      })
      .addCase(requestSuggestions.rejected, (state, action) => {
        console.error('Failed to get suggestions:', action.error.message);
      });
  },
});

export const {
  addMessage,
  removeMessage,
  clearMessages,
  clearConversationHistory,
  setCurrentContextId,
  setSuggestions,
  clearError,
  updateMessage,
} = chatSlice.actions;

export default chatSlice.reducer;
