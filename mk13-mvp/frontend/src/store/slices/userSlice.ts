/**
 * User State Management Slice
 * Manages user authentication, profile, and preferences
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  preferences: {
    theme: 'dark' | 'light';
    language: string;
    notifications: boolean;
    autoSuggestions: boolean;
    voiceEnabled: boolean;
  };
  googleConnected: boolean;
  lastActive: string;
}

export interface UserState {
  currentUser: User | null;
  userId: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  authToken: string | null;
}

const initialState: UserState = {
  currentUser: null,
  userId: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  authToken: null,
};

// Async thunk for user login/creation
export const authenticateUser = createAsyncThunk(
  'user/authenticate',
  async (payload: {
    email: string;
    name: string;
    googleToken?: string;
  }) => {
    // First try to get existing user
    try {
      const getUserResponse = await fetch(`http://localhost:8000/api/users/${payload.email}`);
      
      if (getUserResponse.ok) {
        const userData = await getUserResponse.json();
        return userData.user;
      }
    } catch (error) {
      // User doesn't exist, create new one
    }

    // Create new user
    const createUserResponse = await fetch('http://localhost:8000/api/users', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: payload.email,
        name: payload.name,
        google_token: payload.googleToken,
      }),
    });

    if (!createUserResponse.ok) {
      throw new Error('Failed to create user');
    }

    const userData = await createUserResponse.json();
    return userData.user;
  }
);

// Async thunk for updating user preferences
export const updateUserPreferences = createAsyncThunk(
  'user/updatePreferences',
  async (payload: {
    userId: string;
    preferences: Partial<User['preferences']>;
  }) => {
    const response = await fetch(`http://localhost:8000/api/users/${payload.userId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        preferences: payload.preferences,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to update preferences');
    }

    const data = await response.json();
    return data.user;
  }
);

// Async thunk for Google OAuth
export const connectGoogleAccount = createAsyncThunk(
  'user/connectGoogle',
  async (userId: string) => {
    const response = await fetch(`http://localhost:8000/api/auth/google?user_id=${userId}`);
    
    if (!response.ok) {
      throw new Error('Failed to get Google auth URL');
    }

    const data = await response.json();
    
    // Open Google auth in new window
    window.open(data.auth_url, 'google-auth', 'width=500,height=600');
    
    return data.auth_url;
  }
);

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<User>) => {
      state.currentUser = action.payload;
      state.userId = action.payload.id;
      state.isAuthenticated = true;
    },
    setUserId: (state, action: PayloadAction<string>) => {
      state.userId = action.payload;
    },
    setAuthenticated: (state, action: PayloadAction<boolean>) => {
      state.isAuthenticated = action.payload;
    },
    setAuthToken: (state, action: PayloadAction<string | null>) => {
      state.authToken = action.payload;
    },
    updatePreferences: (state, action: PayloadAction<Partial<User['preferences']>>) => {
      if (state.currentUser) {
        state.currentUser.preferences = {
          ...state.currentUser.preferences,
          ...action.payload,
        };
      }
    },
    setGoogleConnected: (state, action: PayloadAction<boolean>) => {
      if (state.currentUser) {
        state.currentUser.googleConnected = action.payload;
      }
    },
    updateLastActive: (state) => {
      if (state.currentUser) {
        state.currentUser.lastActive = new Date().toISOString();
      }
    },
    clearUser: (state) => {
      state.currentUser = null;
      state.userId = null;
      state.isAuthenticated = false;
      state.authToken = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    // Anonymous user setup for demo purposes
    setAnonymousUser: (state) => {
      const anonymousUser: User = {
        id: 'anonymous',
        email: '<EMAIL>',
        name: 'Demo User',
        preferences: {
          theme: 'dark',
          language: 'en',
          notifications: true,
          autoSuggestions: true,
          voiceEnabled: true,
        },
        googleConnected: false,
        lastActive: new Date().toISOString(),
      };
      
      state.currentUser = anonymousUser;
      state.userId = anonymousUser.id;
      state.isAuthenticated = true;
    },
  },
  extraReducers: (builder) => {
    builder
      // Authenticate User
      .addCase(authenticateUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(authenticateUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentUser = {
          ...action.payload,
          preferences: {
            theme: 'dark',
            language: 'en',
            notifications: true,
            autoSuggestions: true,
            voiceEnabled: true,
            ...action.payload.preferences,
          },
          googleConnected: !!action.payload.google_token,
          lastActive: new Date().toISOString(),
        };
        state.userId = action.payload.id;
        state.isAuthenticated = true;
      })
      .addCase(authenticateUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Authentication failed';
      })
      // Update Preferences
      .addCase(updateUserPreferences.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateUserPreferences.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.currentUser) {
          state.currentUser = {
            ...state.currentUser,
            ...action.payload,
          };
        }
      })
      .addCase(updateUserPreferences.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to update preferences';
      })
      // Connect Google
      .addCase(connectGoogleAccount.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(connectGoogleAccount.fulfilled, (state) => {
        state.isLoading = false;
        // Google connection will be confirmed via callback
      })
      .addCase(connectGoogleAccount.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to connect Google account';
      });
  },
});

export const {
  setUser,
  setUserId,
  setAuthenticated,
  setAuthToken,
  updatePreferences,
  setGoogleConnected,
  updateLastActive,
  clearUser,
  clearError,
  setAnonymousUser,
} = userSlice.actions;

export default userSlice.reducer;
