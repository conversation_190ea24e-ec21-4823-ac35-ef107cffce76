/**
 * Notification State Management Slice
 * Manages system notifications and alerts
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'ai_suggestion';
  title: string;
  message: string;
  timestamp: string;
  autoClose?: boolean;
  duration?: number;
  actions?: Array<{
    label: string;
    action: () => void;
    style?: 'primary' | 'secondary';
  }>;
  metadata?: {
    source?: string;
    contextId?: string;
    actionId?: string;
  };
}

export interface NotificationState {
  notifications: Notification[];
  maxNotifications: number;
  defaultDuration: number;
  settings: {
    enabled: boolean;
    sound: boolean;
    desktop: boolean;
    position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  };
}

const initialState: NotificationState = {
  notifications: [],
  maxNotifications: 5,
  defaultDuration: 5000,
  settings: {
    enabled: true,
    sound: false,
    desktop: false,
    position: 'top-right',
  },
};

const notificationSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    addNotification: (state, action: PayloadAction<Notification>) => {
      // Add new notification
      state.notifications.unshift(action.payload);
      
      // Remove oldest notifications if exceeding max
      if (state.notifications.length > state.maxNotifications) {
        state.notifications = state.notifications.slice(0, state.maxNotifications);
      }
      
      // Request desktop notification permission if enabled
      if (state.settings.desktop && state.settings.enabled) {
        requestDesktopNotification(action.payload);
      }
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(
        notification => notification.id !== action.payload
      );
    },
    clearAllNotifications: (state) => {
      state.notifications = [];
    },
    markAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload);
      if (notification) {
        notification.metadata = {
          ...notification.metadata,
          read: true,
        };
      }
    },
    updateNotificationSettings: (state, action: PayloadAction<Partial<NotificationState['settings']>>) => {
      state.settings = {
        ...state.settings,
        ...action.payload,
      };
    },
    setMaxNotifications: (state, action: PayloadAction<number>) => {
      state.maxNotifications = action.payload;
      
      // Trim notifications if new max is lower
      if (state.notifications.length > action.payload) {
        state.notifications = state.notifications.slice(0, action.payload);
      }
    },
    setDefaultDuration: (state, action: PayloadAction<number>) => {
      state.defaultDuration = action.payload;
    },
    // Convenience actions for different notification types
    addInfoNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'type' | 'timestamp'>>) => {
      const notification: Notification = {
        ...action.payload,
        id: Date.now().toString(),
        type: 'info',
        timestamp: new Date().toISOString(),
        autoClose: action.payload.autoClose !== false,
        duration: action.payload.duration || state.defaultDuration,
      };
      
      state.notifications.unshift(notification);
      
      if (state.notifications.length > state.maxNotifications) {
        state.notifications = state.notifications.slice(0, state.maxNotifications);
      }
    },
    addSuccessNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'type' | 'timestamp'>>) => {
      const notification: Notification = {
        ...action.payload,
        id: Date.now().toString(),
        type: 'success',
        timestamp: new Date().toISOString(),
        autoClose: action.payload.autoClose !== false,
        duration: action.payload.duration || state.defaultDuration,
      };
      
      state.notifications.unshift(notification);
      
      if (state.notifications.length > state.maxNotifications) {
        state.notifications = state.notifications.slice(0, state.maxNotifications);
      }
    },
    addWarningNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'type' | 'timestamp'>>) => {
      const notification: Notification = {
        ...action.payload,
        id: Date.now().toString(),
        type: 'warning',
        timestamp: new Date().toISOString(),
        autoClose: action.payload.autoClose !== false,
        duration: action.payload.duration || state.defaultDuration,
      };
      
      state.notifications.unshift(notification);
      
      if (state.notifications.length > state.maxNotifications) {
        state.notifications = state.notifications.slice(0, state.maxNotifications);
      }
    },
    addErrorNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'type' | 'timestamp'>>) => {
      const notification: Notification = {
        ...action.payload,
        id: Date.now().toString(),
        type: 'error',
        timestamp: new Date().toISOString(),
        autoClose: action.payload.autoClose !== false,
        duration: action.payload.duration || (state.defaultDuration * 2), // Errors stay longer
      };
      
      state.notifications.unshift(notification);
      
      if (state.notifications.length > state.maxNotifications) {
        state.notifications = state.notifications.slice(0, state.maxNotifications);
      }
    },
    addAISuggestionNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'type' | 'timestamp'>>) => {
      const notification: Notification = {
        ...action.payload,
        id: Date.now().toString(),
        type: 'ai_suggestion',
        timestamp: new Date().toISOString(),
        autoClose: action.payload.autoClose !== false,
        duration: action.payload.duration || state.defaultDuration,
      };
      
      state.notifications.unshift(notification);
      
      if (state.notifications.length > state.maxNotifications) {
        state.notifications = state.notifications.slice(0, state.maxNotifications);
      }
    },
  },
});

// Helper function for desktop notifications
const requestDesktopNotification = (notification: Notification) => {
  if ('Notification' in window) {
    if (Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/icon.png', // Add your app icon
        tag: notification.id,
      });
    } else if (Notification.permission !== 'denied') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          new Notification(notification.title, {
            body: notification.message,
            icon: '/icon.png',
            tag: notification.id,
          });
        }
      });
    }
  }
};

export const {
  addNotification,
  removeNotification,
  clearAllNotifications,
  markAsRead,
  updateNotificationSettings,
  setMaxNotifications,
  setDefaultDuration,
  addInfoNotification,
  addSuccessNotification,
  addWarningNotification,
  addErrorNotification,
  addAISuggestionNotification,
} = notificationSlice.actions;

export default notificationSlice.reducer;
