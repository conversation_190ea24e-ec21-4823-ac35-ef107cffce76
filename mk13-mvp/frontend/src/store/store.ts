/**
 * Redux Store Configuration for MK13 MVP
 * Manages global application state
 */

import { configureStore } from '@reduxjs/toolkit';
import uiSlice from './slices/uiSlice';
import chatSlice from './slices/chatSlice';
import socketSlice from './slices/socketSlice';
import userSlice from './slices/userSlice';
import notificationSlice from './slices/notificationSlice';
import contextSlice from './slices/contextSlice';

export const store = configureStore({
  reducer: {
    ui: uiSlice,
    chat: chatSlice,
    socket: socketSlice,
    user: userSlice,
    notifications: notificationSlice,
    context: contextSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore these action types
        ignoredActions: ['socket/connect', 'socket/disconnect'],
        // Ignore these field paths in all actions
        ignoredActionsPaths: ['payload.socket'],
        // Ignore these paths in the state
        ignoredPaths: ['socket.connection'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
