{"version": 3, "sources": ["../../src/immer.ts", "../../src/utils/env.ts", "../../src/utils/errors.ts", "../../src/utils/common.ts", "../../src/utils/plugins.ts", "../../src/core/scope.ts", "../../src/core/finalize.ts", "../../src/core/proxy.ts", "../../src/core/immerClass.ts", "../../src/core/current.ts", "../../src/plugins/patches.ts", "../../src/plugins/mapset.ts"], "sourcesContent": ["import {\n\tIProduce,\n\tIProduceWithPatches,\n\tImmer,\n\tDraft,\n\tImmutable\n} from \"./internal\"\n\nexport {\n\tDraft,\n\tWritableDraft,\n\tImmutable,\n\tPatch,\n\tPatchListener,\n\tProducer,\n\toriginal,\n\tcurrent,\n\tisDraft,\n\tisDraftable,\n\tNOTHING as nothing,\n\tDRAFTABLE as immerable,\n\tfreeze,\n\tObjectish,\n\tStrictMode\n} from \"./internal\"\n\nconst immer = new Immer()\n\n/**\n * The `produce` function takes a value and a \"recipe function\" (whose\n * return value often depends on the base state). The recipe function is\n * free to mutate its first argument however it wants. All mutations are\n * only ever applied to a __copy__ of the base state.\n *\n * Pass only a function to create a \"curried producer\" which relieves you\n * from passing the recipe function every time.\n *\n * Only plain objects and arrays are made mutable. All other objects are\n * considered uncopyable.\n *\n * Note: This function is __bound__ to its `Immer` instance.\n *\n * @param {any} base - the initial state\n * @param {Function} producer - function that receives a proxy of the base state as first argument and which can be freely modified\n * @param {Function} patchListener - optional function that will be called with all the patches produced here\n * @returns {any} a new state, or the initial state if nothing was modified\n */\nexport const produce: IProduce = immer.produce\n\n/**\n * Like `produce`, but `produceWithPatches` always returns a tuple\n * [nextState, patches, inversePatches] (instead of just the next state)\n */\nexport const produceWithPatches: IProduceWithPatches = immer.produceWithPatches.bind(\n\timmer\n)\n\n/**\n * Pass true to automatically freeze all copies created by Immer.\n *\n * Always freeze by default, even in production mode\n */\nexport const setAutoFreeze = immer.setAutoFreeze.bind(immer)\n\n/**\n * Pass true to enable strict shallow copy.\n *\n * By default, immer does not copy the object descriptors such as getter, setter and non-enumrable properties.\n */\nexport const setUseStrictShallowCopy = immer.setUseStrictShallowCopy.bind(immer)\n\n/**\n * Apply an array of Immer patches to the first argument.\n *\n * This function is a producer, which means copy-on-write is in effect.\n */\nexport const applyPatches = immer.applyPatches.bind(immer)\n\n/**\n * Create an Immer draft from the given base state, which may be a draft itself.\n * The draft can be modified until you finalize it with the `finishDraft` function.\n */\nexport const createDraft = immer.createDraft.bind(immer)\n\n/**\n * Finalize an Immer draft from a `createDraft` call, returning the base state\n * (if no changes were made) or a modified copy. The draft must *not* be\n * mutated afterwards.\n *\n * Pass a function as the 2nd argument to generate Immer patches based on the\n * changes that were made.\n */\nexport const finishDraft = immer.finishDraft.bind(immer)\n\n/**\n * This function is actually a no-op, but can be used to cast an immutable type\n * to an draft type and make TypeScript happy\n *\n * @param value\n */\nexport function castDraft<T>(value: T): Draft<T> {\n\treturn value as any\n}\n\n/**\n * This function is actually a no-op, but can be used to cast a mutable type\n * to an immutable type and make TypeScript happy\n * @param value\n */\nexport function castImmutable<T>(value: T): Immutable<T> {\n\treturn value as any\n}\n\nexport {Immer}\n\nexport {enablePatches} from \"./plugins/patches\"\nexport {enableMapSet} from \"./plugins/mapset\"\n", "// Should be no imports here!\n\n/**\n * The sentinel value returned by producers to replace the draft with undefined.\n */\nexport const NOTHING: unique symbol = Symbol.for(\"immer-nothing\")\n\n/**\n * To let Immer treat your class instances as plain immutable objects\n * (albeit with a custom prototype), you must define either an instance property\n * or a static property on each of your custom classes.\n *\n * Otherwise, your class instance will never be drafted, which means it won't be\n * safe to mutate in a produce callback.\n */\nexport const DRAFTABLE: unique symbol = Symbol.for(\"immer-draftable\")\n\nexport const DRAFT_STATE: unique symbol = Symbol.for(\"immer-state\")\n", "export const errors =\n\tprocess.env.NODE_ENV !== \"production\"\n\t\t? [\n\t\t\t\t// All error codes, starting by 0:\n\t\t\t\tfunction(plugin: string) {\n\t\t\t\t\treturn `The plugin for '${plugin}' has not been loaded into Immer. To enable the plugin, import and call \\`enable${plugin}()\\` when initializing your application.`\n\t\t\t\t},\n\t\t\t\tfunction(thing: string) {\n\t\t\t\t\treturn `produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${thing}'`\n\t\t\t\t},\n\t\t\t\t\"This object has been frozen and should not be mutated\",\n\t\t\t\tfunction(data: any) {\n\t\t\t\t\treturn (\n\t\t\t\t\t\t\"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" +\n\t\t\t\t\t\tdata\n\t\t\t\t\t)\n\t\t\t\t},\n\t\t\t\t\"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\",\n\t\t\t\t\"Immer forbids circular references\",\n\t\t\t\t\"The first or second argument to `produce` must be a function\",\n\t\t\t\t\"The third argument to `produce` must be a function or undefined\",\n\t\t\t\t\"First argument to `createDraft` must be a plain object, an array, or an immerable object\",\n\t\t\t\t\"First argument to `finishDraft` must be a draft returned by `createDraft`\",\n\t\t\t\tfunction(thing: string) {\n\t\t\t\t\treturn `'current' expects a draft, got: ${thing}`\n\t\t\t\t},\n\t\t\t\t\"Object.defineProperty() cannot be used on an Immer draft\",\n\t\t\t\t\"Object.setPrototypeOf() cannot be used on an Immer draft\",\n\t\t\t\t\"Immer only supports deleting array indices\",\n\t\t\t\t\"Immer only supports setting array indices and the 'length' property\",\n\t\t\t\tfunction(thing: string) {\n\t\t\t\t\treturn `'original' expects a draft, got: ${thing}`\n\t\t\t\t}\n\t\t\t\t// Note: if more errors are added, the errorOffset in Patches.ts should be increased\n\t\t\t\t// See Patches.ts for additional errors\n\t\t  ]\n\t\t: []\n\nexport function die(error: number, ...args: any[]): never {\n\tif (process.env.NODE_ENV !== \"production\") {\n\t\tconst e = errors[error]\n\t\tconst msg = typeof e === \"function\" ? e.apply(null, args as any) : e\n\t\tthrow new Error(`[Immer] ${msg}`)\n\t}\n\tthrow new Error(\n\t\t`[Immer] minified error nr: ${error}. Full error at: https://bit.ly/3cXEKWf`\n\t)\n}\n", "import {\n\tDRAFT_STATE,\n\tDRAF<PERSON><PERSON><PERSON>,\n\tObjectish,\n\tDrafted,\n\tAnyObject,\n\tAnyMap,\n\tAnySet,\n\tImmerState,\n\tArchType,\n\tdie,\n\tStrictMode\n} from \"../internal\"\n\nexport const getPrototypeOf = Object.getPrototypeOf\n\n/** Returns true if the given value is an Immer draft */\n/*#__PURE__*/\nexport function isDraft(value: any): boolean {\n\treturn !!value && !!value[DRAFT_STATE]\n}\n\n/** Returns true if the given value can be drafted by Immer */\n/*#__PURE__*/\nexport function isDraftable(value: any): boolean {\n\tif (!value) return false\n\treturn (\n\t\tisPlainObject(value) ||\n\t\tArray.isArray(value) ||\n\t\t!!value[DRAFTABLE] ||\n\t\t!!value.constructor?.[DRAFTABLE] ||\n\t\tisMap(value) ||\n\t\tisSet(value)\n\t)\n}\n\nconst objectCtorString = Object.prototype.constructor.toString()\n/*#__PURE__*/\nexport function isPlainObject(value: any): boolean {\n\tif (!value || typeof value !== \"object\") return false\n\tconst proto = getPrototypeOf(value)\n\tif (proto === null) {\n\t\treturn true\n\t}\n\tconst Ctor =\n\t\tObject.hasOwnProperty.call(proto, \"constructor\") && proto.constructor\n\n\tif (Ctor === Object) return true\n\n\treturn (\n\t\ttypeof Ctor == \"function\" &&\n\t\tFunction.toString.call(Ctor) === objectCtorString\n\t)\n}\n\n/** Get the underlying object that is represented by the given draft */\n/*#__PURE__*/\nexport function original<T>(value: T): T | undefined\nexport function original(value: Drafted<any>): any {\n\tif (!isDraft(value)) die(15, value)\n\treturn value[DRAFT_STATE].base_\n}\n\n/**\n * Each iterates a map, set or array.\n * Or, if any other kind of object, all of its own properties.\n * Regardless whether they are enumerable or symbols\n */\nexport function each<T extends Objectish>(\n\tobj: T,\n\titer: (key: string | number, value: any, source: T) => void\n): void\nexport function each(obj: any, iter: any) {\n\tif (getArchtype(obj) === ArchType.Object) {\n\t\tReflect.ownKeys(obj).forEach(key => {\n\t\t\titer(key, obj[key], obj)\n\t\t})\n\t} else {\n\t\tobj.forEach((entry: any, index: any) => iter(index, entry, obj))\n\t}\n}\n\n/*#__PURE__*/\nexport function getArchtype(thing: any): ArchType {\n\tconst state: undefined | ImmerState = thing[DRAFT_STATE]\n\treturn state\n\t\t? state.type_\n\t\t: Array.isArray(thing)\n\t\t? ArchType.Array\n\t\t: isMap(thing)\n\t\t? ArchType.Map\n\t\t: isSet(thing)\n\t\t? ArchType.Set\n\t\t: ArchType.Object\n}\n\n/*#__PURE__*/\nexport function has(thing: any, prop: PropertyKey): boolean {\n\treturn getArchtype(thing) === ArchType.Map\n\t\t? thing.has(prop)\n\t\t: Object.prototype.hasOwnProperty.call(thing, prop)\n}\n\n/*#__PURE__*/\nexport function get(thing: AnyMap | AnyObject, prop: PropertyKey): any {\n\t// @ts-ignore\n\treturn getArchtype(thing) === ArchType.Map ? thing.get(prop) : thing[prop]\n}\n\n/*#__PURE__*/\nexport function set(thing: any, propOrOldValue: PropertyKey, value: any) {\n\tconst t = getArchtype(thing)\n\tif (t === ArchType.Map) thing.set(propOrOldValue, value)\n\telse if (t === ArchType.Set) {\n\t\tthing.add(value)\n\t} else thing[propOrOldValue] = value\n}\n\n/*#__PURE__*/\nexport function is(x: any, y: any): boolean {\n\t// From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js\n\tif (x === y) {\n\t\treturn x !== 0 || 1 / x === 1 / y\n\t} else {\n\t\treturn x !== x && y !== y\n\t}\n}\n\n/*#__PURE__*/\nexport function isMap(target: any): target is AnyMap {\n\treturn target instanceof Map\n}\n\n/*#__PURE__*/\nexport function isSet(target: any): target is AnySet {\n\treturn target instanceof Set\n}\n/*#__PURE__*/\nexport function latest(state: ImmerState): any {\n\treturn state.copy_ || state.base_\n}\n\n/*#__PURE__*/\nexport function shallowCopy(base: any, strict: StrictMode) {\n\tif (isMap(base)) {\n\t\treturn new Map(base)\n\t}\n\tif (isSet(base)) {\n\t\treturn new Set(base)\n\t}\n\tif (Array.isArray(base)) return Array.prototype.slice.call(base)\n\n\tconst isPlain = isPlainObject(base)\n\n\tif (strict === true || (strict === \"class_only\" && !isPlain)) {\n\t\t// Perform a strict copy\n\t\tconst descriptors = Object.getOwnPropertyDescriptors(base)\n\t\tdelete descriptors[DRAFT_STATE as any]\n\t\tlet keys = Reflect.ownKeys(descriptors)\n\t\tfor (let i = 0; i < keys.length; i++) {\n\t\t\tconst key: any = keys[i]\n\t\t\tconst desc = descriptors[key]\n\t\t\tif (desc.writable === false) {\n\t\t\t\tdesc.writable = true\n\t\t\t\tdesc.configurable = true\n\t\t\t}\n\t\t\t// like object.assign, we will read any _own_, get/set accessors. This helps in dealing\n\t\t\t// with libraries that trap values, like mobx or vue\n\t\t\t// unlike object.assign, non-enumerables will be copied as well\n\t\t\tif (desc.get || desc.set)\n\t\t\t\tdescriptors[key] = {\n\t\t\t\t\tconfigurable: true,\n\t\t\t\t\twritable: true, // could live with !!desc.set as well here...\n\t\t\t\t\tenumerable: desc.enumerable,\n\t\t\t\t\tvalue: base[key]\n\t\t\t\t}\n\t\t}\n\t\treturn Object.create(getPrototypeOf(base), descriptors)\n\t} else {\n\t\t// perform a sloppy copy\n\t\tconst proto = getPrototypeOf(base)\n\t\tif (proto !== null && isPlain) {\n\t\t\treturn {...base} // assumption: better inner class optimization than the assign below\n\t\t}\n\t\tconst obj = Object.create(proto)\n\t\treturn Object.assign(obj, base)\n\t}\n}\n\n/**\n * Freezes draftable objects. Returns the original object.\n * By default freezes shallowly, but if the second argument is `true` it will freeze recursively.\n *\n * @param obj\n * @param deep\n */\nexport function freeze<T>(obj: T, deep?: boolean): T\nexport function freeze<T>(obj: any, deep: boolean = false): T {\n\tif (isFrozen(obj) || isDraft(obj) || !isDraftable(obj)) return obj\n\tif (getArchtype(obj) > 1 /* Map or Set */) {\n\t\tobj.set = obj.add = obj.clear = obj.delete = dontMutateFrozenCollections as any\n\t}\n\tObject.freeze(obj)\n\tif (deep)\n\t\t// See #590, don't recurse into non-enumerable / Symbol properties when freezing\n\t\t// So use Object.entries (only string-like, enumerables) instead of each()\n\t\tObject.entries(obj).forEach(([key, value]) => freeze(value, true))\n\treturn obj\n}\n\nfunction dontMutateFrozenCollections() {\n\tdie(2)\n}\n\nexport function isFrozen(obj: any): boolean {\n\treturn Object.isFrozen(obj)\n}\n", "import {\n\tImmerState,\n\t<PERSON>,\n\tDrafted,\n\tImmerB<PERSON>State,\n\tAnyMap,\n\tAnySet,\n\tArchType,\n\tdie\n} from \"../internal\"\n\n/** Plugin utilities */\nconst plugins: {\n\tPatches?: {\n\t\tgeneratePatches_(\n\t\t\tstate: ImmerState,\n\t\t\tbasePath: PatchPath,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tgenerateReplacementPatches_(\n\t\t\tbase: any,\n\t\t\treplacement: any,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tapplyPatches_<T>(draft: T, patches: readonly Patch[]): T\n\t}\n\tMapSet?: {\n\t\tproxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T\n\t\tproxySet_<T extends AnySet>(target: T, parent?: ImmerState): T\n\t}\n} = {}\n\ntype Plugins = typeof plugins\n\nexport function getPlugin<K extends keyof Plugins>(\n\tpluginKey: K\n): Exclude<Plugins[K], undefined> {\n\tconst plugin = plugins[pluginKey]\n\tif (!plugin) {\n\t\tdie(0, pluginKey)\n\t}\n\t// @ts-ignore\n\treturn plugin\n}\n\nexport function loadPlugin<K extends keyof Plugins>(\n\tpluginKey: K,\n\timplementation: Plugins[K]\n): void {\n\tif (!plugins[pluginKey]) plugins[pluginKey] = implementation\n}\n/** Map / Set plugin */\n\nexport interface MapState extends ImmerBaseState {\n\ttype_: ArchType.Map\n\tcopy_: AnyMap | undefined\n\tassigned_: Map<any, boolean> | undefined\n\tbase_: AnyMap\n\trevoked_: boolean\n\tdraft_: Drafted<AnyMap, MapState>\n}\n\nexport interface SetState extends ImmerBaseState {\n\ttype_: ArchType.Set\n\tcopy_: AnySet | undefined\n\tbase_: AnySet\n\tdrafts_: Map<any, Drafted> // maps the original value to the draft value in the new set\n\trevoked_: boolean\n\tdraft_: Drafted<AnySet, SetState>\n}\n\n/** Patches plugin */\n\nexport type PatchPath = (string | number)[]\n", "import {\n\t<PERSON>,\n\t<PERSON>L<PERSON>ener,\n\tDrafted,\n\tImmer,\n\tDRAFT_STATE,\n\tImmerState,\n\tArchType,\n\tgetPlugin\n} from \"../internal\"\n\n/** Each scope represents a `produce` call. */\n\nexport interface ImmerScope {\n\tpatches_?: Patch[]\n\tinversePatches_?: Patch[]\n\tcanAutoFreeze_: boolean\n\tdrafts_: any[]\n\tparent_?: ImmerScope\n\tpatchListener_?: PatchListener\n\timmer_: Immer\n\tunfinalizedDrafts_: number\n}\n\nlet currentScope: ImmerScope | undefined\n\nexport function getCurrentScope() {\n\treturn currentScope!\n}\n\nfunction createScope(\n\tparent_: ImmerScope | undefined,\n\timmer_: Immer\n): ImmerScope {\n\treturn {\n\t\tdrafts_: [],\n\t\tparent_,\n\t\timmer_,\n\t\t// Whenever the modified draft contains a draft from another scope, we\n\t\t// need to prevent auto-freezing so the unowned draft can be finalized.\n\t\tcanAutoFreeze_: true,\n\t\tunfinalizedDrafts_: 0\n\t}\n}\n\nexport function usePatchesInScope(\n\tscope: ImmerScope,\n\tpatchListener?: PatchListener\n) {\n\tif (patchListener) {\n\t\tgetPlugin(\"Patches\") // assert we have the plugin\n\t\tscope.patches_ = []\n\t\tscope.inversePatches_ = []\n\t\tscope.patchListener_ = patchListener\n\t}\n}\n\nexport function revokeScope(scope: ImmerScope) {\n\tleaveScope(scope)\n\tscope.drafts_.forEach(revokeDraft)\n\t// @ts-ignore\n\tscope.drafts_ = null\n}\n\nexport function leaveScope(scope: ImmerScope) {\n\tif (scope === currentScope) {\n\t\tcurrentScope = scope.parent_\n\t}\n}\n\nexport function enterScope(immer: Immer) {\n\treturn (currentScope = createScope(currentScope, immer))\n}\n\nfunction revokeDraft(draft: Drafted) {\n\tconst state: ImmerState = draft[DRAFT_STATE]\n\tif (state.type_ === ArchType.Object || state.type_ === ArchType.Array)\n\t\tstate.revoke_()\n\telse state.revoked_ = true\n}\n", "import {\n\tI<PERSON>Scope,\n\tDRAFT_STATE,\n\tisDraftable,\n\tNOTHING,\n\tPatchP<PERSON>,\n\teach,\n\thas,\n\tfreeze,\n\tImmerState,\n\tisDraft,\n\tSetState,\n\tset,\n\tArchType,\n\tgetPlugin,\n\tdie,\n\trevokeScope,\n\tisFrozen\n} from \"../internal\"\n\nexport function processResult(result: any, scope: ImmerScope) {\n\tscope.unfinalizedDrafts_ = scope.drafts_.length\n\tconst baseDraft = scope.drafts_![0]\n\tconst isReplaced = result !== undefined && result !== baseDraft\n\tif (isReplaced) {\n\t\tif (baseDraft[DRAFT_STATE].modified_) {\n\t\t\trevokeScope(scope)\n\t\t\tdie(4)\n\t\t}\n\t\tif (isDraftable(result)) {\n\t\t\t// Finalize the result in case it contains (or is) a subset of the draft.\n\t\t\tresult = finalize(scope, result)\n\t\t\tif (!scope.parent_) maybeFreeze(scope, result)\n\t\t}\n\t\tif (scope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(\n\t\t\t\tbaseDraft[DRAFT_STATE].base_,\n\t\t\t\tresult,\n\t\t\t\tscope.patches_,\n\t\t\t\tscope.inversePatches_!\n\t\t\t)\n\t\t}\n\t} else {\n\t\t// Finalize the base draft.\n\t\tresult = finalize(scope, baseDraft, [])\n\t}\n\trevokeScope(scope)\n\tif (scope.patches_) {\n\t\tscope.patchListener_!(scope.patches_, scope.inversePatches_!)\n\t}\n\treturn result !== NOTHING ? result : undefined\n}\n\nfunction finalize(rootScope: ImmerScope, value: any, path?: PatchPath) {\n\t// Don't recurse in tho recursive data structures\n\tif (isFrozen(value)) return value\n\n\tconst state: ImmerState = value[DRAFT_STATE]\n\t// A plain object, might need freezing, might contain drafts\n\tif (!state) {\n\t\teach(value, (key, childValue) =>\n\t\t\tfinalizeProperty(rootScope, state, value, key, childValue, path)\n\t\t)\n\t\treturn value\n\t}\n\t// Never finalize drafts owned by another scope.\n\tif (state.scope_ !== rootScope) return value\n\t// Unmodified draft, return the (frozen) original\n\tif (!state.modified_) {\n\t\tmaybeFreeze(rootScope, state.base_, true)\n\t\treturn state.base_\n\t}\n\t// Not finalized yet, let's do that now\n\tif (!state.finalized_) {\n\t\tstate.finalized_ = true\n\t\tstate.scope_.unfinalizedDrafts_--\n\t\tconst result = state.copy_\n\t\t// Finalize all children of the copy\n\t\t// For sets we clone before iterating, otherwise we can get in endless loop due to modifying during iteration, see #628\n\t\t// To preserve insertion order in all cases we then clear the set\n\t\t// And we let finalizeProperty know it needs to re-add non-draft children back to the target\n\t\tlet resultEach = result\n\t\tlet isSet = false\n\t\tif (state.type_ === ArchType.Set) {\n\t\t\tresultEach = new Set(result)\n\t\t\tresult.clear()\n\t\t\tisSet = true\n\t\t}\n\t\teach(resultEach, (key, childValue) =>\n\t\t\tfinalizeProperty(rootScope, state, result, key, childValue, path, isSet)\n\t\t)\n\t\t// everything inside is frozen, we can freeze here\n\t\tmaybeFreeze(rootScope, result, false)\n\t\t// first time finalizing, let's create those patches\n\t\tif (path && rootScope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generatePatches_(\n\t\t\t\tstate,\n\t\t\t\tpath,\n\t\t\t\trootScope.patches_,\n\t\t\t\trootScope.inversePatches_!\n\t\t\t)\n\t\t}\n\t}\n\treturn state.copy_\n}\n\nfunction finalizeProperty(\n\trootScope: ImmerScope,\n\tparentState: undefined | ImmerState,\n\ttargetObject: any,\n\tprop: string | number,\n\tchildValue: any,\n\trootPath?: PatchPath,\n\ttargetIsSet?: boolean\n) {\n\tif (process.env.NODE_ENV !== \"production\" && childValue === targetObject)\n\t\tdie(5)\n\tif (isDraft(childValue)) {\n\t\tconst path =\n\t\t\trootPath &&\n\t\t\tparentState &&\n\t\t\tparentState!.type_ !== ArchType.Set && // Set objects are atomic since they have no keys.\n\t\t\t!has((parentState as Exclude<ImmerState, SetState>).assigned_!, prop) // Skip deep patches for assigned keys.\n\t\t\t\t? rootPath!.concat(prop)\n\t\t\t\t: undefined\n\t\t// Drafts owned by `scope` are finalized here.\n\t\tconst res = finalize(rootScope, childValue, path)\n\t\tset(targetObject, prop, res)\n\t\t// Drafts from another scope must prevented to be frozen\n\t\t// if we got a draft back from finalize, we're in a nested produce and shouldn't freeze\n\t\tif (isDraft(res)) {\n\t\t\trootScope.canAutoFreeze_ = false\n\t\t} else return\n\t} else if (targetIsSet) {\n\t\ttargetObject.add(childValue)\n\t}\n\t// Search new objects for unfinalized drafts. Frozen objects should never contain drafts.\n\tif (isDraftable(childValue) && !isFrozen(childValue)) {\n\t\tif (!rootScope.immer_.autoFreeze_ && rootScope.unfinalizedDrafts_ < 1) {\n\t\t\t// optimization: if an object is not a draft, and we don't have to\n\t\t\t// deepfreeze everything, and we are sure that no drafts are left in the remaining object\n\t\t\t// cause we saw and finalized all drafts already; we can stop visiting the rest of the tree.\n\t\t\t// This benefits especially adding large data tree's without further processing.\n\t\t\t// See add-data.js perf test\n\t\t\treturn\n\t\t}\n\t\tfinalize(rootScope, childValue)\n\t\t// Immer deep freezes plain objects, so if there is no parent state, we freeze as well\n\t\t// Per #590, we never freeze symbolic properties. Just to make sure don't accidentally interfere\n\t\t// with other frameworks.\n\t\tif (\n\t\t\t(!parentState || !parentState.scope_.parent_) &&\n\t\t\ttypeof prop !== \"symbol\" &&\n\t\t\tObject.prototype.propertyIsEnumerable.call(targetObject, prop)\n\t\t)\n\t\t\tmaybeFreeze(rootScope, childValue)\n\t}\n}\n\nfunction maybeFreeze(scope: ImmerScope, value: any, deep = false) {\n\t// we never freeze for a non-root scope; as it would prevent pruning for drafts inside wrapping objects\n\tif (!scope.parent_ && scope.immer_.autoFreeze_ && scope.canAutoFreeze_) {\n\t\tfreeze(value, deep)\n\t}\n}\n", "import {\n\teach,\n\thas,\n\tis,\n\tisDraftable,\n\tshallowCopy,\n\tlatest,\n\tImmerBaseState,\n\tImmerState,\n\tDrafted,\n\tAnyObject,\n\tAnyArray,\n\tObjectish,\n\tgetCurrentScope,\n\tgetPrototypeOf,\n\tDRAFT_STATE,\n\tdie,\n\tcreateProxy,\n\tArchType,\n\tImmerScope\n} from \"../internal\"\n\ninterface ProxyBaseState extends ImmerBaseState {\n\tassigned_: {\n\t\t[property: string]: boolean\n\t}\n\tparent_?: ImmerState\n\trevoke_(): void\n}\n\nexport interface ProxyObjectState extends ProxyBaseState {\n\ttype_: ArchType.Object\n\tbase_: any\n\tcopy_: any\n\tdraft_: Drafted<AnyObject, ProxyObjectState>\n}\n\nexport interface ProxyArrayState extends ProxyBaseState {\n\ttype_: ArchType.Array\n\tbase_: AnyArray\n\tcopy_: AnyArray | null\n\tdraft_: Drafted<AnyArray, ProxyArrayState>\n}\n\ntype ProxyState = ProxyObjectState | ProxyArrayState\n\n/**\n * Returns a new draft of the `base` object.\n *\n * The second argument is the parent draft-state (used internally).\n */\nexport function createProxyProxy<T extends Objectish>(\n\tbase: T,\n\tparent?: ImmerState\n): Drafted<T, ProxyState> {\n\tconst isArray = Array.isArray(base)\n\tconst state: ProxyState = {\n\t\ttype_: isArray ? ArchType.Array : (ArchType.Object as any),\n\t\t// Track which produce call this is associated with.\n\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t// True for both shallow and deep changes.\n\t\tmodified_: false,\n\t\t// Used during finalization.\n\t\tfinalized_: false,\n\t\t// Track which properties have been assigned (true) or deleted (false).\n\t\tassigned_: {},\n\t\t// The parent draft state.\n\t\tparent_: parent,\n\t\t// The base state.\n\t\tbase_: base,\n\t\t// The base proxy.\n\t\tdraft_: null as any, // set below\n\t\t// The base copy with any updated values.\n\t\tcopy_: null,\n\t\t// Called by the `produce` function.\n\t\trevoke_: null as any,\n\t\tisManual_: false\n\t}\n\n\t// the traps must target something, a bit like the 'real' base.\n\t// but also, we need to be able to determine from the target what the relevant state is\n\t// (to avoid creating traps per instance to capture the state in closure,\n\t// and to avoid creating weird hidden properties as well)\n\t// So the trick is to use 'state' as the actual 'target'! (and make sure we intercept everything)\n\t// Note that in the case of an array, we put the state in an array to have better Reflect defaults ootb\n\tlet target: T = state as any\n\tlet traps: ProxyHandler<object | Array<any>> = objectTraps\n\tif (isArray) {\n\t\ttarget = [state] as any\n\t\ttraps = arrayTraps\n\t}\n\n\tconst {revoke, proxy} = Proxy.revocable(target, traps)\n\tstate.draft_ = proxy as any\n\tstate.revoke_ = revoke\n\treturn proxy as any\n}\n\n/**\n * Object drafts\n */\nexport const objectTraps: ProxyHandler<ProxyState> = {\n\tget(state, prop) {\n\t\tif (prop === DRAFT_STATE) return state\n\n\t\tconst source = latest(state)\n\t\tif (!has(source, prop)) {\n\t\t\t// non-existing or non-own property...\n\t\t\treturn readPropFromProto(state, source, prop)\n\t\t}\n\t\tconst value = source[prop]\n\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\treturn value\n\t\t}\n\t\t// Check for existing draft in modified state.\n\t\t// Assigned values are never drafted. This catches any drafts we created, too.\n\t\tif (value === peek(state.base_, prop)) {\n\t\t\tprepareCopy(state)\n\t\t\treturn (state.copy_![prop as any] = createProxy(value, state))\n\t\t}\n\t\treturn value\n\t},\n\thas(state, prop) {\n\t\treturn prop in latest(state)\n\t},\n\townKeys(state) {\n\t\treturn Reflect.ownKeys(latest(state))\n\t},\n\tset(\n\t\tstate: ProxyObjectState,\n\t\tprop: string /* strictly not, but helps TS */,\n\t\tvalue\n\t) {\n\t\tconst desc = getDescriptorFromProto(latest(state), prop)\n\t\tif (desc?.set) {\n\t\t\t// special case: if this write is captured by a setter, we have\n\t\t\t// to trigger it with the correct context\n\t\t\tdesc.set.call(state.draft_, value)\n\t\t\treturn true\n\t\t}\n\t\tif (!state.modified_) {\n\t\t\t// the last check is because we need to be able to distinguish setting a non-existing to undefined (which is a change)\n\t\t\t// from setting an existing property with value undefined to undefined (which is not a change)\n\t\t\tconst current = peek(latest(state), prop)\n\t\t\t// special case, if we assigning the original value to a draft, we can ignore the assignment\n\t\t\tconst currentState: ProxyObjectState = current?.[DRAFT_STATE]\n\t\t\tif (currentState && currentState.base_ === value) {\n\t\t\t\tstate.copy_![prop] = value\n\t\t\t\tstate.assigned_[prop] = false\n\t\t\t\treturn true\n\t\t\t}\n\t\t\tif (is(value, current) && (value !== undefined || has(state.base_, prop)))\n\t\t\t\treturn true\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t}\n\n\t\tif (\n\t\t\t(state.copy_![prop] === value &&\n\t\t\t\t// special case: handle new props with value 'undefined'\n\t\t\t\t(value !== undefined || prop in state.copy_)) ||\n\t\t\t// special case: NaN\n\t\t\t(Number.isNaN(value) && Number.isNaN(state.copy_![prop]))\n\t\t)\n\t\t\treturn true\n\n\t\t// @ts-ignore\n\t\tstate.copy_![prop] = value\n\t\tstate.assigned_[prop] = true\n\t\treturn true\n\t},\n\tdeleteProperty(state, prop: string) {\n\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\tif (peek(state.base_, prop) !== undefined || prop in state.base_) {\n\t\t\tstate.assigned_[prop] = false\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t} else {\n\t\t\t// if an originally not assigned property was deleted\n\t\t\tdelete state.assigned_[prop]\n\t\t}\n\t\tif (state.copy_) {\n\t\t\tdelete state.copy_[prop]\n\t\t}\n\t\treturn true\n\t},\n\t// Note: We never coerce `desc.value` into an Immer draft, because we can't make\n\t// the same guarantee in ES5 mode.\n\tgetOwnPropertyDescriptor(state, prop) {\n\t\tconst owner = latest(state)\n\t\tconst desc = Reflect.getOwnPropertyDescriptor(owner, prop)\n\t\tif (!desc) return desc\n\t\treturn {\n\t\t\twritable: true,\n\t\t\tconfigurable: state.type_ !== ArchType.Array || prop !== \"length\",\n\t\t\tenumerable: desc.enumerable,\n\t\t\tvalue: owner[prop]\n\t\t}\n\t},\n\tdefineProperty() {\n\t\tdie(11)\n\t},\n\tgetPrototypeOf(state) {\n\t\treturn getPrototypeOf(state.base_)\n\t},\n\tsetPrototypeOf() {\n\t\tdie(12)\n\t}\n}\n\n/**\n * Array drafts\n */\n\nconst arrayTraps: ProxyHandler<[ProxyArrayState]> = {}\neach(objectTraps, (key, fn) => {\n\t// @ts-ignore\n\tarrayTraps[key] = function() {\n\t\targuments[0] = arguments[0][0]\n\t\treturn fn.apply(this, arguments)\n\t}\n})\narrayTraps.deleteProperty = function(state, prop) {\n\tif (process.env.NODE_ENV !== \"production\" && isNaN(parseInt(prop as any)))\n\t\tdie(13)\n\t// @ts-ignore\n\treturn arrayTraps.set!.call(this, state, prop, undefined)\n}\narrayTraps.set = function(state, prop, value) {\n\tif (\n\t\tprocess.env.NODE_ENV !== \"production\" &&\n\t\tprop !== \"length\" &&\n\t\tisNaN(parseInt(prop as any))\n\t)\n\t\tdie(14)\n\treturn objectTraps.set!.call(this, state[0], prop, value, state[0])\n}\n\n// Access a property without creating an Immer draft.\nfunction peek(draft: Drafted, prop: PropertyKey) {\n\tconst state = draft[DRAFT_STATE]\n\tconst source = state ? latest(state) : draft\n\treturn source[prop]\n}\n\nfunction readPropFromProto(state: ImmerState, source: any, prop: PropertyKey) {\n\tconst desc = getDescriptorFromProto(source, prop)\n\treturn desc\n\t\t? `value` in desc\n\t\t\t? desc.value\n\t\t\t: // This is a very special case, if the prop is a getter defined by the\n\t\t\t  // prototype, we should invoke it with the draft as context!\n\t\t\t  desc.get?.call(state.draft_)\n\t\t: undefined\n}\n\nfunction getDescriptorFromProto(\n\tsource: any,\n\tprop: PropertyKey\n): PropertyDescriptor | undefined {\n\t// 'in' checks proto!\n\tif (!(prop in source)) return undefined\n\tlet proto = getPrototypeOf(source)\n\twhile (proto) {\n\t\tconst desc = Object.getOwnPropertyDescriptor(proto, prop)\n\t\tif (desc) return desc\n\t\tproto = getPrototypeOf(proto)\n\t}\n\treturn undefined\n}\n\nexport function markChanged(state: ImmerState) {\n\tif (!state.modified_) {\n\t\tstate.modified_ = true\n\t\tif (state.parent_) {\n\t\t\tmarkChanged(state.parent_)\n\t\t}\n\t}\n}\n\nexport function prepareCopy(state: {\n\tbase_: any\n\tcopy_: any\n\tscope_: ImmerScope\n}) {\n\tif (!state.copy_) {\n\t\tstate.copy_ = shallowCopy(\n\t\t\tstate.base_,\n\t\t\tstate.scope_.immer_.useStrictShallowCopy_\n\t\t)\n\t}\n}\n", "import {\n\tIProduceWithPatches,\n\tIProduce,\n\tImmerState,\n\tDrafted,\n\tisDraftable,\n\tprocessResult,\n\tPatch,\n\tObjectish,\n\tDRAFT_STATE,\n\tDraft,\n\tPatchListener,\n\tisDraft,\n\tisMap,\n\tisSet,\n\tcreateProxyProxy,\n\tgetPlugin,\n\tdie,\n\tenterScope,\n\trevokeScope,\n\tleaveScope,\n\tusePatchesInScope,\n\tgetCurrentScope,\n\tNOTHING,\n\tfreeze,\n\tcurrent\n} from \"../internal\"\n\ninterface ProducersFns {\n\tproduce: IProduce\n\tproduceWithPatches: IProduceWithPatches\n}\n\nexport type StrictMode = boolean | \"class_only\";\n\nexport class Immer implements ProducersFns {\n\tautoFreeze_: boolean = true\n\tuseStrictShallowCopy_: StrictMode = false\n\n\tconstructor(config?: {\n\t\tautoFreeze?: boolean\n\t\tuseStrictShallowCopy?: StrictMode\n\t}) {\n\t\tif (typeof config?.autoFreeze === \"boolean\")\n\t\t\tthis.setAutoFreeze(config!.autoFreeze)\n\t\tif (typeof config?.useStrictShallowCopy === \"boolean\")\n\t\t\tthis.setUseStrictShallowCopy(config!.useStrictShallowCopy)\n\t}\n\n\t/**\n\t * The `produce` function takes a value and a \"recipe function\" (whose\n\t * return value often depends on the base state). The recipe function is\n\t * free to mutate its first argument however it wants. All mutations are\n\t * only ever applied to a __copy__ of the base state.\n\t *\n\t * Pass only a function to create a \"curried producer\" which relieves you\n\t * from passing the recipe function every time.\n\t *\n\t * Only plain objects and arrays are made mutable. All other objects are\n\t * considered uncopyable.\n\t *\n\t * Note: This function is __bound__ to its `Immer` instance.\n\t *\n\t * @param {any} base - the initial state\n\t * @param {Function} recipe - function that receives a proxy of the base state as first argument and which can be freely modified\n\t * @param {Function} patchListener - optional function that will be called with all the patches produced here\n\t * @returns {any} a new state, or the initial state if nothing was modified\n\t */\n\tproduce: IProduce = (base: any, recipe?: any, patchListener?: any) => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\" && typeof recipe !== \"function\") {\n\t\t\tconst defaultBase = recipe\n\t\t\trecipe = base\n\n\t\t\tconst self = this\n\t\t\treturn function curriedProduce(\n\t\t\t\tthis: any,\n\t\t\t\tbase = defaultBase,\n\t\t\t\t...args: any[]\n\t\t\t) {\n\t\t\t\treturn self.produce(base, (draft: Drafted) => recipe.call(this, draft, ...args)) // prettier-ignore\n\t\t\t}\n\t\t}\n\n\t\tif (typeof recipe !== \"function\") die(6)\n\t\tif (patchListener !== undefined && typeof patchListener !== \"function\")\n\t\t\tdie(7)\n\n\t\tlet result\n\n\t\t// Only plain objects, arrays, and \"immerable classes\" are drafted.\n\t\tif (isDraftable(base)) {\n\t\t\tconst scope = enterScope(this)\n\t\t\tconst proxy = createProxy(base, undefined)\n\t\t\tlet hasError = true\n\t\t\ttry {\n\t\t\t\tresult = recipe(proxy)\n\t\t\t\thasError = false\n\t\t\t} finally {\n\t\t\t\t// finally instead of catch + rethrow better preserves original stack\n\t\t\t\tif (hasError) revokeScope(scope)\n\t\t\t\telse leaveScope(scope)\n\t\t\t}\n\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\treturn processResult(result, scope)\n\t\t} else if (!base || typeof base !== \"object\") {\n\t\t\tresult = recipe(base)\n\t\t\tif (result === undefined) result = base\n\t\t\tif (result === NOTHING) result = undefined\n\t\t\tif (this.autoFreeze_) freeze(result, true)\n\t\t\tif (patchListener) {\n\t\t\t\tconst p: Patch[] = []\n\t\t\t\tconst ip: Patch[] = []\n\t\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(base, result, p, ip)\n\t\t\t\tpatchListener(p, ip)\n\t\t\t}\n\t\t\treturn result\n\t\t} else die(1, base)\n\t}\n\n\tproduceWithPatches: IProduceWithPatches = (base: any, recipe?: any): any => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\") {\n\t\t\treturn (state: any, ...args: any[]) =>\n\t\t\t\tthis.produceWithPatches(state, (draft: any) => base(draft, ...args))\n\t\t}\n\n\t\tlet patches: Patch[], inversePatches: Patch[]\n\t\tconst result = this.produce(base, recipe, (p: Patch[], ip: Patch[]) => {\n\t\t\tpatches = p\n\t\t\tinversePatches = ip\n\t\t})\n\t\treturn [result, patches!, inversePatches!]\n\t}\n\n\tcreateDraft<T extends Objectish>(base: T): Draft<T> {\n\t\tif (!isDraftable(base)) die(8)\n\t\tif (isDraft(base)) base = current(base)\n\t\tconst scope = enterScope(this)\n\t\tconst proxy = createProxy(base, undefined)\n\t\tproxy[DRAFT_STATE].isManual_ = true\n\t\tleaveScope(scope)\n\t\treturn proxy as any\n\t}\n\n\tfinishDraft<D extends Draft<any>>(\n\t\tdraft: D,\n\t\tpatchListener?: PatchListener\n\t): D extends Draft<infer T> ? T : never {\n\t\tconst state: ImmerState = draft && (draft as any)[DRAFT_STATE]\n\t\tif (!state || !state.isManual_) die(9)\n\t\tconst {scope_: scope} = state\n\t\tusePatchesInScope(scope, patchListener)\n\t\treturn processResult(undefined, scope)\n\t}\n\n\t/**\n\t * Pass true to automatically freeze all copies created by Immer.\n\t *\n\t * By default, auto-freezing is enabled.\n\t */\n\tsetAutoFreeze(value: boolean) {\n\t\tthis.autoFreeze_ = value\n\t}\n\n\t/**\n\t * Pass true to enable strict shallow copy.\n\t *\n\t * By default, immer does not copy the object descriptors such as getter, setter and non-enumrable properties.\n\t */\n\tsetUseStrictShallowCopy(value: StrictMode) {\n\t\tthis.useStrictShallowCopy_ = value\n\t}\n\n\tapplyPatches<T extends Objectish>(base: T, patches: readonly Patch[]): T {\n\t\t// If a patch replaces the entire state, take that replacement as base\n\t\t// before applying patches\n\t\tlet i: number\n\t\tfor (i = patches.length - 1; i >= 0; i--) {\n\t\t\tconst patch = patches[i]\n\t\t\tif (patch.path.length === 0 && patch.op === \"replace\") {\n\t\t\t\tbase = patch.value\n\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\t\t// If there was a patch that replaced the entire state, start from the\n\t\t// patch after that.\n\t\tif (i > -1) {\n\t\t\tpatches = patches.slice(i + 1)\n\t\t}\n\n\t\tconst applyPatchesImpl = getPlugin(\"Patches\").applyPatches_\n\t\tif (isDraft(base)) {\n\t\t\t// N.B: never hits if some patch a replacement, patches are never drafts\n\t\t\treturn applyPatchesImpl(base, patches)\n\t\t}\n\t\t// Otherwise, produce a copy of the base state.\n\t\treturn this.produce(base, (draft: Drafted) =>\n\t\t\tapplyPatchesImpl(draft, patches)\n\t\t)\n\t}\n}\n\nexport function createProxy<T extends Objectish>(\n\tvalue: T,\n\tparent?: ImmerState\n): Drafted<T, ImmerState> {\n\t// precondition: createProxy should be guarded by isDraftable, so we know we can safely draft\n\tconst draft: Drafted = isMap(value)\n\t\t? getPlugin(\"MapSet\").proxyMap_(value, parent)\n\t\t: isSet(value)\n\t\t? getPlugin(\"MapSet\").proxySet_(value, parent)\n\t\t: createProxyProxy(value, parent)\n\n\tconst scope = parent ? parent.scope_ : getCurrentScope()\n\tscope.drafts_.push(draft)\n\treturn draft\n}\n", "import {\n\tdie,\n\tisDraft,\n\tshallowCopy,\n\teach,\n\tDRAFT_STATE,\n\tset,\n\tImmerState,\n\tisDraftable,\n\tisFrozen\n} from \"../internal\"\n\n/** Takes a snapshot of the current state of a draft and finalizes it (but without freezing). This is a great utility to print the current state during debugging (no Proxies in the way). The output of current can also be safely leaked outside the producer. */\nexport function current<T>(value: T): T\nexport function current(value: any): any {\n\tif (!isDraft(value)) die(10, value)\n\treturn currentImpl(value)\n}\n\nfunction currentImpl(value: any): any {\n\tif (!isDraftable(value) || isFrozen(value)) return value\n\tconst state: ImmerState | undefined = value[DRAFT_STATE]\n\tlet copy: any\n\tif (state) {\n\t\tif (!state.modified_) return state.base_\n\t\t// Optimization: avoid generating new drafts during copying\n\t\tstate.finalized_ = true\n\t\tcopy = shallowCopy(value, state.scope_.immer_.useStrictShallowCopy_)\n\t} else {\n\t\tcopy = shallowCopy(value, true)\n\t}\n\t// recurse\n\teach(copy, (key, childValue) => {\n\t\tset(copy, key, currentImpl(childValue))\n\t})\n\tif (state) {\n\t\tstate.finalized_ = false\n\t}\n\treturn copy\n}\n", "import {immerable} from \"../immer\"\nimport {\n\tImmerState,\n\tPatch,\n\tSetState,\n\tProxyArrayState,\n\tMapState,\n\tProxyObjectState,\n\tPatchPath,\n\tget,\n\teach,\n\thas,\n\tgetArchtype,\n\tgetPrototypeOf,\n\tisSet,\n\tisMap,\n\tloadPlugin,\n\tArchType,\n\tdie,\n\tisDraft,\n\tisDraftable,\n\tNOTHING,\n\terrors\n} from \"../internal\"\n\nexport function enablePatches() {\n\tconst errorOffset = 16\n\tif (process.env.NODE_ENV !== \"production\") {\n\t\terrors.push(\n\t\t\t'Sets cannot have \"replace\" patches.',\n\t\t\tfunction(op: string) {\n\t\t\t\treturn \"Unsupported patch operation: \" + op\n\t\t\t},\n\t\t\tfunction(path: string) {\n\t\t\t\treturn \"Cannot apply patch, path doesn't resolve: \" + path\n\t\t\t},\n\t\t\t\"Patching reserved attributes like __proto__, prototype and constructor is not allowed\"\n\t\t)\n\t}\n\n\tconst REPLACE = \"replace\"\n\tconst ADD = \"add\"\n\tconst REMOVE = \"remove\"\n\n\tfunction generatePatches_(\n\t\tstate: ImmerState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tswitch (state.type_) {\n\t\t\tcase ArchType.Object:\n\t\t\tcase ArchType.Map:\n\t\t\t\treturn generatePatchesFromAssigned(\n\t\t\t\t\tstate,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t\tcase ArchType.Array:\n\t\t\t\treturn generateArrayPatches(state, basePath, patches, inversePatches)\n\t\t\tcase ArchType.Set:\n\t\t\t\treturn generateSetPatches(\n\t\t\t\t\t(state as any) as SetState,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t}\n\t}\n\n\tfunction generateArrayPatches(\n\t\tstate: ProxyArrayState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, assigned_} = state\n\t\tlet copy_ = state.copy_!\n\n\t\t// Reduce complexity by ensuring `base` is never longer.\n\t\tif (copy_.length < base_.length) {\n\t\t\t// @ts-ignore\n\t\t\t;[base_, copy_] = [copy_, base_]\n\t\t\t;[patches, inversePatches] = [inversePatches, patches]\n\t\t}\n\n\t\t// Process replaced indices.\n\t\tfor (let i = 0; i < base_.length; i++) {\n\t\t\tif (assigned_[i] && copy_[i] !== base_[i]) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t\t})\n\t\t\t\tinversePatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(base_[i])\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\n\t\t// Process added indices.\n\t\tfor (let i = base_.length; i < copy_.length; i++) {\n\t\t\tconst path = basePath.concat([i])\n\t\t\tpatches.push({\n\t\t\t\top: ADD,\n\t\t\t\tpath,\n\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t})\n\t\t}\n\t\tfor (let i = copy_.length - 1; base_.length <= i; --i) {\n\t\t\tconst path = basePath.concat([i])\n\t\t\tinversePatches.push({\n\t\t\t\top: REMOVE,\n\t\t\t\tpath\n\t\t\t})\n\t\t}\n\t}\n\n\t// This is used for both Map objects and normal objects.\n\tfunction generatePatchesFromAssigned(\n\t\tstate: MapState | ProxyObjectState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tconst {base_, copy_} = state\n\t\teach(state.assigned_!, (key, assignedValue) => {\n\t\t\tconst origValue = get(base_, key)\n\t\t\tconst value = get(copy_!, key)\n\t\t\tconst op = !assignedValue ? REMOVE : has(base_, key) ? REPLACE : ADD\n\t\t\tif (origValue === value && op === REPLACE) return\n\t\t\tconst path = basePath.concat(key as any)\n\t\t\tpatches.push(op === REMOVE ? {op, path} : {op, path, value})\n\t\t\tinversePatches.push(\n\t\t\t\top === ADD\n\t\t\t\t\t? {op: REMOVE, path}\n\t\t\t\t\t: op === REMOVE\n\t\t\t\t\t? {op: ADD, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t\t\t: {op: REPLACE, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t)\n\t\t})\n\t}\n\n\tfunction generateSetPatches(\n\t\tstate: SetState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, copy_} = state\n\n\t\tlet i = 0\n\t\tbase_.forEach((value: any) => {\n\t\t\tif (!copy_!.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t\ti = 0\n\t\tcopy_!.forEach((value: any) => {\n\t\t\tif (!base_.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t}\n\n\tfunction generateReplacementPatches_(\n\t\tbaseValue: any,\n\t\treplacement: any,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tpatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: replacement === NOTHING ? undefined : replacement\n\t\t})\n\t\tinversePatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: baseValue\n\t\t})\n\t}\n\n\tfunction applyPatches_<T>(draft: T, patches: readonly Patch[]): T {\n\t\tpatches.forEach(patch => {\n\t\t\tconst {path, op} = patch\n\n\t\t\tlet base: any = draft\n\t\t\tfor (let i = 0; i < path.length - 1; i++) {\n\t\t\t\tconst parentType = getArchtype(base)\n\t\t\t\tlet p = path[i]\n\t\t\t\tif (typeof p !== \"string\" && typeof p !== \"number\") {\n\t\t\t\t\tp = \"\" + p\n\t\t\t\t}\n\n\t\t\t\t// See #738, avoid prototype pollution\n\t\t\t\tif (\n\t\t\t\t\t(parentType === ArchType.Object || parentType === ArchType.Array) &&\n\t\t\t\t\t(p === \"__proto__\" || p === \"constructor\")\n\t\t\t\t)\n\t\t\t\t\tdie(errorOffset + 3)\n\t\t\t\tif (typeof base === \"function\" && p === \"prototype\")\n\t\t\t\t\tdie(errorOffset + 3)\n\t\t\t\tbase = get(base, p)\n\t\t\t\tif (typeof base !== \"object\") die(errorOffset + 2, path.join(\"/\"))\n\t\t\t}\n\n\t\t\tconst type = getArchtype(base)\n\t\t\tconst value = deepClonePatchValue(patch.value) // used to clone patch to ensure original patch is not modified, see #411\n\t\t\tconst key = path[path.length - 1]\n\t\t\tswitch (op) {\n\t\t\t\tcase REPLACE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase ArchType.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\t\tcase ArchType.Set:\n\t\t\t\t\t\t\tdie(errorOffset)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t// if value is an object, then it's assigned by reference\n\t\t\t\t\t\t\t// in the following add or remove ops, the value field inside the patch will also be modifyed\n\t\t\t\t\t\t\t// so we use value from the cloned patch\n\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase ADD:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase ArchType.Array:\n\t\t\t\t\t\t\treturn key === \"-\"\n\t\t\t\t\t\t\t\t? base.push(value)\n\t\t\t\t\t\t\t\t: base.splice(key as any, 0, value)\n\t\t\t\t\t\tcase ArchType.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\tcase ArchType.Set:\n\t\t\t\t\t\t\treturn base.add(value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase REMOVE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase ArchType.Array:\n\t\t\t\t\t\t\treturn base.splice(key as any, 1)\n\t\t\t\t\t\tcase ArchType.Map:\n\t\t\t\t\t\t\treturn base.delete(key)\n\t\t\t\t\t\tcase ArchType.Set:\n\t\t\t\t\t\t\treturn base.delete(patch.value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn delete base[key]\n\t\t\t\t\t}\n\t\t\t\tdefault:\n\t\t\t\t\tdie(errorOffset + 1, op)\n\t\t\t}\n\t\t})\n\n\t\treturn draft\n\t}\n\n\t// optimize: this is quite a performance hit, can we detect intelligently when it is needed?\n\t// E.g. auto-draft when new objects from outside are assigned and modified?\n\t// (See failing test when deepClone just returns obj)\n\tfunction deepClonePatchValue<T>(obj: T): T\n\tfunction deepClonePatchValue(obj: any) {\n\t\tif (!isDraftable(obj)) return obj\n\t\tif (Array.isArray(obj)) return obj.map(deepClonePatchValue)\n\t\tif (isMap(obj))\n\t\t\treturn new Map(\n\t\t\t\tArray.from(obj.entries()).map(([k, v]) => [k, deepClonePatchValue(v)])\n\t\t\t)\n\t\tif (isSet(obj)) return new Set(Array.from(obj).map(deepClonePatchValue))\n\t\tconst cloned = Object.create(getPrototypeOf(obj))\n\t\tfor (const key in obj) cloned[key] = deepClonePatchValue(obj[key])\n\t\tif (has(obj, immerable)) cloned[immerable] = obj[immerable]\n\t\treturn cloned\n\t}\n\n\tfunction clonePatchValueIfNeeded<T>(obj: T): T {\n\t\tif (isDraft(obj)) {\n\t\t\treturn deepClonePatchValue(obj)\n\t\t} else return obj\n\t}\n\n\tloadPlugin(\"Patches\", {\n\t\tapplyPatches_,\n\t\tgeneratePatches_,\n\t\tgenerateReplacementPatches_\n\t})\n}\n", "// types only!\nimport {\n\tImmerState,\n\tAnyMap,\n\tAnySet,\n\tMapState,\n\tSetState,\n\tDRAFT_STATE,\n\tgetCurrentScope,\n\tlatest,\n\tisDraftable,\n\tcreateProxy,\n\tloadPlugin,\n\tmarkChanged,\n\tdie,\n\tArchType,\n\teach\n} from \"../internal\"\n\nexport function enableMapSet() {\n\tclass DraftMap extends Map {\n\t\t[DRAFT_STATE]: MapState\n\n\t\tconstructor(target: AnyMap, parent?: ImmerState) {\n\t\t\tsuper()\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ArchType.Map,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tassigned_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this as any,\n\t\t\t\tisManual_: false,\n\t\t\t\trevoked_: false\n\t\t\t}\n\t\t}\n\n\t\tget size(): number {\n\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t}\n\n\t\thas(key: any): boolean {\n\t\t\treturn latest(this[DRAFT_STATE]).has(key)\n\t\t}\n\n\t\tset(key: any, value: any) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!latest(state).has(key) || latest(state).get(key) !== value) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t\tstate.copy_!.set(key, value)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tdelete(key: any): boolean {\n\t\t\tif (!this.has(key)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareMapCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\tif (state.base_.has(key)) {\n\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t} else {\n\t\t\t\tstate.assigned_!.delete(key)\n\t\t\t}\n\t\t\tstate.copy_!.delete(key)\n\t\t\treturn true\n\t\t}\n\n\t\tclear() {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_ = new Map()\n\t\t\t\teach(state.base_, key => {\n\t\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t\t})\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tforEach(cb: (value: any, key: any, self: any) => void, thisArg?: any) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tlatest(state).forEach((_value: any, key: any, _map: any) => {\n\t\t\t\tcb.call(thisArg, this.get(key), key, this)\n\t\t\t})\n\t\t}\n\n\t\tget(key: any): any {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tconst value = latest(state).get(key)\n\t\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\t\treturn value\n\t\t\t}\n\t\t\tif (value !== state.base_.get(key)) {\n\t\t\t\treturn value // either already drafted or reassigned\n\t\t\t}\n\t\t\t// despite what it looks, this creates a draft only once, see above condition\n\t\t\tconst draft = createProxy(value, state)\n\t\t\tprepareMapCopy(state)\n\t\t\tstate.copy_!.set(key, draft)\n\t\t\treturn draft\n\t\t}\n\n\t\tkeys(): IterableIterator<any> {\n\t\t\treturn latest(this[DRAFT_STATE]).keys()\n\t\t}\n\n\t\tvalues(): IterableIterator<any> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[Symbol.iterator]: () => this.values(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tentries(): IterableIterator<[any, any]> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[Symbol.iterator]: () => this.entries(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue: [r.value, value]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\t[Symbol.iterator]() {\n\t\t\treturn this.entries()\n\t\t}\n\t}\n\n\tfunction proxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftMap(target, parent)\n\t}\n\n\tfunction prepareMapCopy(state: MapState) {\n\t\tif (!state.copy_) {\n\t\t\tstate.assigned_ = new Map()\n\t\t\tstate.copy_ = new Map(state.base_)\n\t\t}\n\t}\n\n\tclass DraftSet extends Set {\n\t\t[DRAFT_STATE]: SetState\n\t\tconstructor(target: AnySet, parent?: ImmerState) {\n\t\t\tsuper()\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ArchType.Set,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this,\n\t\t\t\tdrafts_: new Map(),\n\t\t\t\trevoked_: false,\n\t\t\t\tisManual_: false\n\t\t\t}\n\t\t}\n\n\t\tget size(): number {\n\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t}\n\n\t\thas(value: any): boolean {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\t// bit of trickery here, to be able to recognize both the value, and the draft of its value\n\t\t\tif (!state.copy_) {\n\t\t\t\treturn state.base_.has(value)\n\t\t\t}\n\t\t\tif (state.copy_.has(value)) return true\n\t\t\tif (state.drafts_.has(value) && state.copy_.has(state.drafts_.get(value)))\n\t\t\t\treturn true\n\t\t\treturn false\n\t\t}\n\n\t\tadd(value: any): any {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!this.has(value)) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.add(value)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tdelete(value: any): any {\n\t\t\tif (!this.has(value)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\treturn (\n\t\t\t\tstate.copy_!.delete(value) ||\n\t\t\t\t(state.drafts_.has(value)\n\t\t\t\t\t? state.copy_!.delete(state.drafts_.get(value))\n\t\t\t\t\t: /* istanbul ignore next */ false)\n\t\t\t)\n\t\t}\n\n\t\tclear() {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tvalues(): IterableIterator<any> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.values()\n\t\t}\n\n\t\tentries(): IterableIterator<[any, any]> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.entries()\n\t\t}\n\n\t\tkeys(): IterableIterator<any> {\n\t\t\treturn this.values()\n\t\t}\n\n\t\t[Symbol.iterator]() {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tforEach(cb: any, thisArg?: any) {\n\t\t\tconst iterator = this.values()\n\t\t\tlet result = iterator.next()\n\t\t\twhile (!result.done) {\n\t\t\t\tcb.call(thisArg, result.value, result.value, this)\n\t\t\t\tresult = iterator.next()\n\t\t\t}\n\t\t}\n\t}\n\tfunction proxySet_<T extends AnySet>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftSet(target, parent)\n\t}\n\n\tfunction prepareSetCopy(state: SetState) {\n\t\tif (!state.copy_) {\n\t\t\t// create drafts for all entries to preserve insertion order\n\t\t\tstate.copy_ = new Set()\n\t\t\tstate.base_.forEach(value => {\n\t\t\t\tif (isDraftable(value)) {\n\t\t\t\t\tconst draft = createProxy(value, state)\n\t\t\t\t\tstate.drafts_.set(value, draft)\n\t\t\t\t\tstate.copy_!.add(draft)\n\t\t\t\t} else {\n\t\t\t\t\tstate.copy_!.add(value)\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"MapSet\", {proxyMap_, proxySet_})\n}\n"], "mappings": "ubAAA,IAAAA,GAAA,GAAAC,GAAAD,GAAA,WAAAE,EAAA,iBAAAC,GAAA,cAAAC,GAAA,kBAAAC,GAAA,gBAAAC,GAAA,YAAAC,GAAA,iBAAAC,GAAA,kBAAAC,GAAA,gBAAAC,GAAA,WAAAC,EAAA,cAAAC,EAAA,YAAAC,EAAA,gBAAAC,EAAA,YAAAC,EAAA,aAAAC,GAAA,YAAAC,GAAA,uBAAAC,GAAA,kBAAAC,GAAA,4BAAAC,KAAA,eAAAC,GAAArB,ICKO,IAAMsB,EAAyB,OAAO,IAAI,eAAe,EAUnDC,EAA2B,OAAO,IAAI,iBAAiB,EAEvDC,EAA6B,OAAO,IAAI,aAAa,ECqB3D,SAASC,EAAIC,KAAkBC,EAAoB,CAMzD,MAAM,IAAI,MACT,8BAA8BD,0CAC/B,CACD,CCjCO,IAAME,EAAiB,OAAO,eAI9B,SAASC,EAAQC,EAAqB,CAC5C,MAAO,CAAC,CAACA,GAAS,CAAC,CAACA,EAAMC,CAAW,CACtC,CAIO,SAASC,EAAYF,EAAqB,CAChD,OAAKA,EAEJG,GAAcH,CAAK,GACnB,MAAM,QAAQA,CAAK,GACnB,CAAC,CAACA,EAAMI,CAAS,GACjB,CAAC,CAACJ,EAAM,cAAcI,CAAS,GAC/BC,EAAML,CAAK,GACXM,EAAMN,CAAK,EAPO,EASpB,CAEA,IAAMO,GAAmB,OAAO,UAAU,YAAY,SAAS,EAExD,SAASJ,GAAcH,EAAqB,CAClD,GAAI,CAACA,GAAS,OAAOA,GAAU,SAAU,MAAO,GAChD,IAAMQ,EAAQV,EAAeE,CAAK,EAClC,GAAIQ,IAAU,KACb,MAAO,GAER,IAAMC,EACL,OAAO,eAAe,KAAKD,EAAO,aAAa,GAAKA,EAAM,YAE3D,OAAIC,IAAS,OAAe,GAG3B,OAAOA,GAAQ,YACf,SAAS,SAAS,KAAKA,CAAI,IAAMF,EAEnC,CAKO,SAASG,GAASV,EAA0B,CAClD,OAAKD,EAAQC,CAAK,GAAGW,EAAI,GAAIX,CAAK,EAC3BA,EAAMC,CAAW,EAAEW,CAC3B,CAWO,SAASC,EAAKC,EAAUC,EAAW,CACrCC,EAAYF,CAAG,IAAM,EACxB,QAAQ,QAAQA,CAAG,EAAE,QAAQG,GAAO,CACnCF,EAAKE,EAAKH,EAAIG,CAAG,EAAGH,CAAG,CACxB,CAAC,EAEDA,EAAI,QAAQ,CAACI,EAAYC,IAAeJ,EAAKI,EAAOD,EAAOJ,CAAG,CAAC,CAEjE,CAGO,SAASE,EAAYI,EAAsB,CACjD,IAAMC,EAAgCD,EAAMnB,CAAW,EACvD,OAAOoB,EACJA,EAAMC,EACN,MAAM,QAAQF,CAAK,IAEnBf,EAAMe,CAAK,IAEXd,EAAMc,CAAK,KAGf,CAGO,SAASG,EAAIH,EAAYI,EAA4B,CAC3D,OAAOR,EAAYI,CAAK,IAAM,EAC3BA,EAAM,IAAII,CAAI,EACd,OAAO,UAAU,eAAe,KAAKJ,EAAOI,CAAI,CACpD,CAGO,SAASC,EAAIL,EAA2BI,EAAwB,CAEtE,OAAOR,EAAYI,CAAK,IAAM,EAAeA,EAAM,IAAII,CAAI,EAAIJ,EAAMI,CAAI,CAC1E,CAGO,SAASE,EAAIN,EAAYO,EAA6B3B,EAAY,CACxE,IAAM4B,EAAIZ,EAAYI,CAAK,EACvBQ,IAAM,EAAcR,EAAM,IAAIO,EAAgB3B,CAAK,EAC9C4B,IAAM,EACdR,EAAM,IAAIpB,CAAK,EACToB,EAAMO,CAAc,EAAI3B,CAChC,CAGO,SAAS6B,GAAGC,EAAQC,EAAiB,CAE3C,OAAID,IAAMC,EACFD,IAAM,GAAK,EAAIA,IAAM,EAAIC,EAEzBD,IAAMA,GAAKC,IAAMA,CAE1B,CAGO,SAAS1B,EAAM2B,EAA+B,CACpD,OAAOA,aAAkB,GAC1B,CAGO,SAAS1B,EAAM0B,EAA+B,CACpD,OAAOA,aAAkB,GAC1B,CAEO,SAASC,EAAOZ,EAAwB,CAC9C,OAAOA,EAAMa,GAASb,EAAMT,CAC7B,CAGO,SAASuB,EAAYC,EAAWC,EAAoB,CAC1D,GAAIhC,EAAM+B,CAAI,EACb,OAAO,IAAI,IAAIA,CAAI,EAEpB,GAAI9B,EAAM8B,CAAI,EACb,OAAO,IAAI,IAAIA,CAAI,EAEpB,GAAI,MAAM,QAAQA,CAAI,EAAG,OAAO,MAAM,UAAU,MAAM,KAAKA,CAAI,EAE/D,IAAME,EAAUnC,GAAciC,CAAI,EAElC,GAAIC,IAAW,IAASA,IAAW,cAAgB,CAACC,EAAU,CAE7D,IAAMC,EAAc,OAAO,0BAA0BH,CAAI,EACzD,OAAOG,EAAYtC,CAAkB,EACrC,IAAIuC,EAAO,QAAQ,QAAQD,CAAW,EACtC,QAASE,EAAI,EAAGA,EAAID,EAAK,OAAQC,IAAK,CACrC,IAAMxB,EAAWuB,EAAKC,CAAC,EACjBC,EAAOH,EAAYtB,CAAG,EACxByB,EAAK,WAAa,KACrBA,EAAK,SAAW,GAChBA,EAAK,aAAe,KAKjBA,EAAK,KAAOA,EAAK,OACpBH,EAAYtB,CAAG,EAAI,CAClB,aAAc,GACd,SAAU,GACV,WAAYyB,EAAK,WACjB,MAAON,EAAKnB,CAAG,CAChB,GAEF,OAAO,OAAO,OAAOnB,EAAesC,CAAI,EAAGG,CAAW,MAChD,CAEN,IAAM/B,EAAQV,EAAesC,CAAI,EACjC,GAAI5B,IAAU,MAAQ8B,EACrB,MAAO,CAAC,GAAGF,CAAI,EAEhB,IAAMtB,EAAM,OAAO,OAAON,CAAK,EAC/B,OAAO,OAAO,OAAOM,EAAKsB,CAAI,EAEhC,CAUO,SAASO,EAAU7B,EAAU8B,EAAgB,GAAU,CAC7D,OAAIC,EAAS/B,CAAG,GAAKf,EAAQe,CAAG,GAAK,CAACZ,EAAYY,CAAG,IACjDE,EAAYF,CAAG,EAAI,IACtBA,EAAI,IAAMA,EAAI,IAAMA,EAAI,MAAQA,EAAI,OAASgC,IAE9C,OAAO,OAAOhC,CAAG,EACb8B,GAGH,OAAO,QAAQ9B,CAAG,EAAE,QAAQ,CAAC,CAACG,EAAKjB,CAAK,IAAM2C,EAAO3C,EAAO,EAAI,CAAC,GAC3Dc,CACR,CAEA,SAASgC,IAA8B,CACtCnC,EAAI,CAAC,CACN,CAEO,SAASkC,EAAS/B,EAAmB,CAC3C,OAAO,OAAO,SAASA,CAAG,CAC3B,CC5MA,IAAMiC,GAoBF,CAAC,EAIE,SAASC,EACfC,EACiC,CACjC,IAAMC,EAASH,GAAQE,CAAS,EAChC,OAAKC,GACJC,EAAI,EAAGF,CAAS,EAGVC,CACR,CAEO,SAASE,EACfH,EACAI,EACO,CACFN,GAAQE,CAAS,IAAGF,GAAQE,CAAS,EAAII,EAC/C,CC5BA,IAAIC,EAEG,SAASC,GAAkB,CACjC,OAAOD,CACR,CAEA,SAASE,GACRC,EACAC,EACa,CACb,MAAO,CACNC,EAAS,CAAC,EACVF,IACAC,IAGAE,EAAgB,GAChBC,EAAoB,CACrB,CACD,CAEO,SAASC,GACfC,EACAC,EACC,CACGA,IACHC,EAAU,SAAS,EACnBF,EAAMG,EAAW,CAAC,EAClBH,EAAMI,EAAkB,CAAC,EACzBJ,EAAMK,EAAiBJ,EAEzB,CAEO,SAASK,EAAYN,EAAmB,CAC9CO,EAAWP,CAAK,EAChBA,EAAMJ,EAAQ,QAAQY,EAAW,EAEjCR,EAAMJ,EAAU,IACjB,CAEO,SAASW,EAAWP,EAAmB,CACzCA,IAAUT,IACbA,EAAeS,EAAMN,EAEvB,CAEO,SAASe,GAAWC,EAAc,CACxC,OAAQnB,EAAeE,GAAYF,EAAcmB,CAAK,CACvD,CAEA,SAASF,GAAYG,EAAgB,CACpC,IAAMC,EAAoBD,EAAME,CAAW,EACvCD,EAAME,IAAU,GAAmBF,EAAME,IAAU,EACtDF,EAAMG,EAAQ,EACVH,EAAMI,EAAW,EACvB,CC3DO,SAASC,GAAcC,EAAaC,EAAmB,CAC7DA,EAAMC,EAAqBD,EAAME,EAAQ,OACzC,IAAMC,EAAYH,EAAME,EAAS,CAAC,EAElC,OADmBH,IAAW,QAAaA,IAAWI,GAEjDA,EAAUC,CAAW,EAAEC,IAC1BC,EAAYN,CAAK,EACjBO,EAAI,CAAC,GAEFC,EAAYT,CAAM,IAErBA,EAASU,GAAST,EAAOD,CAAM,EAC1BC,EAAMU,GAASC,GAAYX,EAAOD,CAAM,GAE1CC,EAAMY,GACTC,EAAU,SAAS,EAAEC,EACpBX,EAAUC,CAAW,EAAEW,EACvBhB,EACAC,EAAMY,EACNZ,EAAMgB,CACP,GAIDjB,EAASU,GAAST,EAAOG,EAAW,CAAC,CAAC,EAEvCG,EAAYN,CAAK,EACbA,EAAMY,GACTZ,EAAMiB,EAAgBjB,EAAMY,EAAUZ,EAAMgB,CAAgB,EAEtDjB,IAAWmB,EAAUnB,EAAS,MACtC,CAEA,SAASU,GAASU,EAAuBC,EAAYC,EAAkB,CAEtE,GAAIC,EAASF,CAAK,EAAG,OAAOA,EAE5B,IAAMG,EAAoBH,EAAMhB,CAAW,EAE3C,GAAI,CAACmB,EACJ,OAAAC,EAAKJ,EAAO,CAACK,EAAKC,IACjBC,GAAiBR,EAAWI,EAAOH,EAAOK,EAAKC,EAAYL,CAAI,CAChE,EACOD,EAGR,GAAIG,EAAMK,IAAWT,EAAW,OAAOC,EAEvC,GAAI,CAACG,EAAMlB,EACV,OAAAM,GAAYQ,EAAWI,EAAMR,EAAO,EAAI,EACjCQ,EAAMR,EAGd,GAAI,CAACQ,EAAMM,EAAY,CACtBN,EAAMM,EAAa,GACnBN,EAAMK,EAAO3B,IACb,IAAMF,EAASwB,EAAMO,EAKjBC,EAAahC,EACbiC,EAAQ,GACRT,EAAMU,IAAU,IACnBF,EAAa,IAAI,IAAIhC,CAAM,EAC3BA,EAAO,MAAM,EACbiC,EAAQ,IAETR,EAAKO,EAAY,CAACN,EAAKC,IACtBC,GAAiBR,EAAWI,EAAOxB,EAAQ0B,EAAKC,EAAYL,EAAMW,CAAK,CACxE,EAEArB,GAAYQ,EAAWpB,EAAQ,EAAK,EAEhCsB,GAAQF,EAAUP,GACrBC,EAAU,SAAS,EAAEqB,EACpBX,EACAF,EACAF,EAAUP,EACVO,EAAUH,CACX,EAGF,OAAOO,EAAMO,CACd,CAEA,SAASH,GACRR,EACAgB,EACAC,EACAC,EACAX,EACAY,EACAC,EACC,CAGD,GAAIC,EAAQd,CAAU,EAAG,CACxB,IAAML,EACLiB,GACAH,GACAA,EAAaF,IAAU,GACvB,CAACQ,EAAKN,EAA8CO,EAAYL,CAAI,EACjEC,EAAU,OAAOD,CAAI,EACrB,OAEEM,EAAMlC,GAASU,EAAWO,EAAYL,CAAI,EAIhD,GAHAuB,EAAIR,EAAcC,EAAMM,CAAG,EAGvBH,EAAQG,CAAG,EACdxB,EAAU0B,EAAiB,OACrB,aACGN,GACVH,EAAa,IAAIV,CAAU,EAG5B,GAAIlB,EAAYkB,CAAU,GAAK,CAACJ,EAASI,CAAU,EAAG,CACrD,GAAI,CAACP,EAAU2B,EAAOC,GAAe5B,EAAUlB,EAAqB,EAMnE,OAEDQ,GAASU,EAAWO,CAAU,GAK5B,CAACS,GAAe,CAACA,EAAYP,EAAOlB,IACrC,OAAO2B,GAAS,UAChB,OAAO,UAAU,qBAAqB,KAAKD,EAAcC,CAAI,GAE7D1B,GAAYQ,EAAWO,CAAU,EAEpC,CAEA,SAASf,GAAYX,EAAmBoB,EAAY4B,EAAO,GAAO,CAE7D,CAAChD,EAAMU,GAAWV,EAAM8C,EAAOC,GAAe/C,EAAM6C,GACvDI,EAAO7B,EAAO4B,CAAI,CAEpB,CCjHO,SAASE,GACfC,EACAC,EACyB,CACzB,IAAMC,EAAU,MAAM,QAAQF,CAAI,EAC5BG,EAAoB,CACzBC,EAAOF,MAEPG,EAAQJ,EAASA,EAAOI,EAASC,EAAgB,EAEjDC,EAAW,GAEXC,EAAY,GAEZC,EAAW,CAAC,EAEZC,EAAST,EAETU,EAAOX,EAEPY,EAAQ,KAERC,EAAO,KAEPC,EAAS,KACTC,EAAW,EACZ,EAQIC,EAAYb,EACZc,EAA2CC,GAC3ChB,IACHc,EAAS,CAACb,CAAK,EACfc,EAAQE,GAGT,GAAM,CAAC,OAAAC,EAAQ,MAAAC,CAAK,EAAI,MAAM,UAAUL,EAAQC,CAAK,EACrD,OAAAd,EAAMS,EAASS,EACflB,EAAMW,EAAUM,EACTC,CACR,CAKO,IAAMH,GAAwC,CACpD,IAAIf,EAAOmB,EAAM,CAChB,GAAIA,IAASC,EAAa,OAAOpB,EAEjC,IAAMqB,EAASC,EAAOtB,CAAK,EAC3B,GAAI,CAACuB,EAAIF,EAAQF,CAAI,EAEpB,OAAOK,GAAkBxB,EAAOqB,EAAQF,CAAI,EAE7C,IAAMM,EAAQJ,EAAOF,CAAI,EACzB,OAAInB,EAAMK,GAAc,CAACqB,EAAYD,CAAK,EAClCA,EAIJA,IAAUE,GAAK3B,EAAMQ,EAAOW,CAAI,GACnCS,GAAY5B,CAAK,EACTA,EAAMU,EAAOS,CAAW,EAAIU,EAAYJ,EAAOzB,CAAK,GAEtDyB,CACR,EACA,IAAIzB,EAAOmB,EAAM,CAChB,OAAOA,KAAQG,EAAOtB,CAAK,CAC5B,EACA,QAAQA,EAAO,CACd,OAAO,QAAQ,QAAQsB,EAAOtB,CAAK,CAAC,CACrC,EACA,IACCA,EACAmB,EACAM,EACC,CACD,IAAMK,EAAOC,GAAuBT,EAAOtB,CAAK,EAAGmB,CAAI,EACvD,GAAIW,GAAM,IAGT,OAAAA,EAAK,IAAI,KAAK9B,EAAMS,EAAQgB,CAAK,EAC1B,GAER,GAAI,CAACzB,EAAMI,EAAW,CAGrB,IAAM4B,EAAUL,GAAKL,EAAOtB,CAAK,EAAGmB,CAAI,EAElCc,EAAiCD,IAAUZ,CAAW,EAC5D,GAAIa,GAAgBA,EAAazB,IAAUiB,EAC1C,OAAAzB,EAAMU,EAAOS,CAAI,EAAIM,EACrBzB,EAAMM,EAAUa,CAAI,EAAI,GACjB,GAER,GAAIe,GAAGT,EAAOO,CAAO,IAAMP,IAAU,QAAaF,EAAIvB,EAAMQ,EAAOW,CAAI,GACtE,MAAO,GACRS,GAAY5B,CAAK,EACjBmC,EAAYnC,CAAK,EAGlB,OACEA,EAAMU,EAAOS,CAAI,IAAMM,IAEtBA,IAAU,QAAaN,KAAQnB,EAAMU,IAEtC,OAAO,MAAMe,CAAK,GAAK,OAAO,MAAMzB,EAAMU,EAAOS,CAAI,CAAC,IAKxDnB,EAAMU,EAAOS,CAAI,EAAIM,EACrBzB,EAAMM,EAAUa,CAAI,EAAI,IACjB,EACR,EACA,eAAenB,EAAOmB,EAAc,CAEnC,OAAIQ,GAAK3B,EAAMQ,EAAOW,CAAI,IAAM,QAAaA,KAAQnB,EAAMQ,GAC1DR,EAAMM,EAAUa,CAAI,EAAI,GACxBS,GAAY5B,CAAK,EACjBmC,EAAYnC,CAAK,GAGjB,OAAOA,EAAMM,EAAUa,CAAI,EAExBnB,EAAMU,GACT,OAAOV,EAAMU,EAAMS,CAAI,EAEjB,EACR,EAGA,yBAAyBnB,EAAOmB,EAAM,CACrC,IAAMiB,EAAQd,EAAOtB,CAAK,EACpB8B,EAAO,QAAQ,yBAAyBM,EAAOjB,CAAI,EACzD,OAAKW,GACE,CACN,SAAU,GACV,aAAc9B,EAAMC,IAAU,GAAkBkB,IAAS,SACzD,WAAYW,EAAK,WACjB,MAAOM,EAAMjB,CAAI,CAClB,CACD,EACA,gBAAiB,CAChBkB,EAAI,EAAE,CACP,EACA,eAAerC,EAAO,CACrB,OAAOsC,EAAetC,EAAMQ,CAAK,CAClC,EACA,gBAAiB,CAChB6B,EAAI,EAAE,CACP,CACD,EAMMrB,EAA8C,CAAC,EACrDuB,EAAKxB,GAAa,CAACyB,EAAKC,IAAO,CAE9BzB,EAAWwB,CAAG,EAAI,UAAW,CAC5B,iBAAU,CAAC,EAAI,UAAU,CAAC,EAAE,CAAC,EACtBC,EAAG,MAAM,KAAM,SAAS,CAChC,CACD,CAAC,EACDzB,EAAW,eAAiB,SAAShB,EAAOmB,EAAM,CAIjD,OAAOH,EAAW,IAAK,KAAK,KAAMhB,EAAOmB,EAAM,MAAS,CACzD,EACAH,EAAW,IAAM,SAAShB,EAAOmB,EAAMM,EAAO,CAO7C,OAAOV,GAAY,IAAK,KAAK,KAAMf,EAAM,CAAC,EAAGmB,EAAMM,EAAOzB,EAAM,CAAC,CAAC,CACnE,EAGA,SAAS2B,GAAKe,EAAgBvB,EAAmB,CAChD,IAAMnB,EAAQ0C,EAAMtB,CAAW,EAE/B,OADepB,EAAQsB,EAAOtB,CAAK,EAAI0C,GACzBvB,CAAI,CACnB,CAEA,SAASK,GAAkBxB,EAAmBqB,EAAaF,EAAmB,CAC7E,IAAMW,EAAOC,GAAuBV,EAAQF,CAAI,EAChD,OAAOW,EACJ,UAAWA,EACVA,EAAK,MAGLA,EAAK,KAAK,KAAK9B,EAAMS,CAAM,EAC5B,MACJ,CAEA,SAASsB,GACRV,EACAF,EACiC,CAEjC,GAAI,EAAEA,KAAQE,GAAS,OACvB,IAAIsB,EAAQL,EAAejB,CAAM,EACjC,KAAOsB,GAAO,CACb,IAAMb,EAAO,OAAO,yBAAyBa,EAAOxB,CAAI,EACxD,GAAIW,EAAM,OAAOA,EACjBa,EAAQL,EAAeK,CAAK,EAG9B,CAEO,SAASR,EAAYnC,EAAmB,CACzCA,EAAMI,IACVJ,EAAMI,EAAY,GACdJ,EAAMO,GACT4B,EAAYnC,EAAMO,CAAO,EAG5B,CAEO,SAASqB,GAAY5B,EAIzB,CACGA,EAAMU,IACVV,EAAMU,EAAQkC,EACb5C,EAAMQ,EACNR,EAAME,EAAO2C,EAAOC,CACrB,EAEF,CChQO,IAAMC,EAAN,KAAoC,CAI1C,YAAYC,EAGT,CANH,KAAAC,EAAuB,GACvB,KAAAC,EAAoC,GA+BpC,aAAoB,CAACC,EAAWC,EAAcC,IAAwB,CAErE,GAAI,OAAOF,GAAS,YAAc,OAAOC,GAAW,WAAY,CAC/D,IAAME,EAAcF,EACpBA,EAASD,EAET,IAAMI,EAAO,KACb,OAAO,SAENJ,EAAOG,KACJE,EACF,CACD,OAAOD,EAAK,QAAQJ,EAAOM,GAAmBL,EAAO,KAAK,KAAMK,EAAO,GAAGD,CAAI,CAAC,CAChF,EAGG,OAAOJ,GAAW,YAAYM,EAAI,CAAC,EACnCL,IAAkB,QAAa,OAAOA,GAAkB,YAC3DK,EAAI,CAAC,EAEN,IAAIC,EAGJ,GAAIC,EAAYT,CAAI,EAAG,CACtB,IAAMU,EAAQC,GAAW,IAAI,EACvBC,EAAQC,EAAYb,EAAM,MAAS,EACrCc,EAAW,GACf,GAAI,CACHN,EAASP,EAAOW,CAAK,EACrBE,EAAW,EACZ,QAAE,CAEGA,EAAUC,EAAYL,CAAK,EAC1BM,EAAWN,CAAK,CACtB,CACA,OAAAO,GAAkBP,EAAOR,CAAa,EAC/BgB,GAAcV,EAAQE,CAAK,UACxB,CAACV,GAAQ,OAAOA,GAAS,SAAU,CAK7C,GAJAQ,EAASP,EAAOD,CAAI,EAChBQ,IAAW,SAAWA,EAASR,GAC/BQ,IAAWW,IAASX,EAAS,QAC7B,KAAKV,GAAasB,EAAOZ,EAAQ,EAAI,EACrCN,EAAe,CAClB,IAAMmB,EAAa,CAAC,EACdC,EAAc,CAAC,EACrBC,EAAU,SAAS,EAAEC,EAA4BxB,EAAMQ,EAAQa,EAAGC,CAAE,EACpEpB,EAAcmB,EAAGC,CAAE,EAEpB,OAAOd,OACDD,EAAI,EAAGP,CAAI,CACnB,EAEA,wBAA0C,CAACA,EAAWC,IAAsB,CAE3E,GAAI,OAAOD,GAAS,WACnB,MAAO,CAACyB,KAAepB,IACtB,KAAK,mBAAmBoB,EAAQnB,GAAeN,EAAKM,EAAO,GAAGD,CAAI,CAAC,EAGrE,IAAIqB,EAAkBC,EAKtB,MAAO,CAJQ,KAAK,QAAQ3B,EAAMC,EAAQ,CAACoB,EAAYC,IAAgB,CACtEI,EAAUL,EACVM,EAAiBL,CAClB,CAAC,EACeI,EAAUC,CAAe,CAC1C,EA1FK,OAAO9B,GAAQ,YAAe,WACjC,KAAK,cAAcA,EAAQ,UAAU,EAClC,OAAOA,GAAQ,sBAAyB,WAC3C,KAAK,wBAAwBA,EAAQ,oBAAoB,CAC3D,CAwFA,YAAiCG,EAAmB,CAC9CS,EAAYT,CAAI,GAAGO,EAAI,CAAC,EACzBqB,EAAQ5B,CAAI,IAAGA,EAAO6B,GAAQ7B,CAAI,GACtC,IAAMU,EAAQC,GAAW,IAAI,EACvBC,EAAQC,EAAYb,EAAM,MAAS,EACzC,OAAAY,EAAMkB,CAAW,EAAEC,EAAY,GAC/Bf,EAAWN,CAAK,EACTE,CACR,CAEA,YACCN,EACAJ,EACuC,CACvC,IAAMuB,EAAoBnB,GAAUA,EAAcwB,CAAW,GACzD,CAACL,GAAS,CAACA,EAAMM,IAAWxB,EAAI,CAAC,EACrC,GAAM,CAACyB,EAAQtB,CAAK,EAAIe,EACxB,OAAAR,GAAkBP,EAAOR,CAAa,EAC/BgB,GAAc,OAAWR,CAAK,CACtC,CAOA,cAAcuB,EAAgB,CAC7B,KAAKnC,EAAcmC,CACpB,CAOA,wBAAwBA,EAAmB,CAC1C,KAAKlC,EAAwBkC,CAC9B,CAEA,aAAkCjC,EAAS0B,EAA8B,CAGxE,IAAIQ,EACJ,IAAKA,EAAIR,EAAQ,OAAS,EAAGQ,GAAK,EAAGA,IAAK,CACzC,IAAMC,EAAQT,EAAQQ,CAAC,EACvB,GAAIC,EAAM,KAAK,SAAW,GAAKA,EAAM,KAAO,UAAW,CACtDnC,EAAOmC,EAAM,MACb,OAKED,EAAI,KACPR,EAAUA,EAAQ,MAAMQ,EAAI,CAAC,GAG9B,IAAME,EAAmBb,EAAU,SAAS,EAAEc,EAC9C,OAAIT,EAAQ5B,CAAI,EAERoC,EAAiBpC,EAAM0B,CAAO,EAG/B,KAAK,QAAQ1B,EAAOM,GAC1B8B,EAAiB9B,EAAOoB,CAAO,CAChC,CACD,CACD,EAEO,SAASb,EACfoB,EACAK,EACyB,CAEzB,IAAMhC,EAAiBiC,EAAMN,CAAK,EAC/BV,EAAU,QAAQ,EAAEiB,EAAUP,EAAOK,CAAM,EAC3CG,EAAMR,CAAK,EACXV,EAAU,QAAQ,EAAEmB,EAAUT,EAAOK,CAAM,EAC3CK,GAAiBV,EAAOK,CAAM,EAGjC,OADcA,EAASA,EAAON,EAASY,EAAgB,GACjDC,EAAQ,KAAKvC,CAAK,EACjBA,CACR,CC3MO,SAASwC,GAAQC,EAAiB,CACxC,OAAKC,EAAQD,CAAK,GAAGE,EAAI,GAAIF,CAAK,EAC3BG,GAAYH,CAAK,CACzB,CAEA,SAASG,GAAYH,EAAiB,CACrC,GAAI,CAACI,EAAYJ,CAAK,GAAKK,EAASL,CAAK,EAAG,OAAOA,EACnD,IAAMM,EAAgCN,EAAMO,CAAW,EACnDC,EACJ,GAAIF,EAAO,CACV,GAAI,CAACA,EAAMG,EAAW,OAAOH,EAAMI,EAEnCJ,EAAMK,EAAa,GACnBH,EAAOI,EAAYZ,EAAOM,EAAMO,EAAOC,EAAOC,CAAqB,OAEnEP,EAAOI,EAAYZ,EAAO,EAAI,EAG/B,OAAAgB,EAAKR,EAAM,CAACS,EAAKC,IAAe,CAC/BC,EAAIX,EAAMS,EAAKd,GAAYe,CAAU,CAAC,CACvC,CAAC,EACGZ,IACHA,EAAMK,EAAa,IAEbH,CACR,CCdO,SAASY,IAAgB,CAe/B,IAAMC,EAAU,UACVC,EAAM,MACNC,EAAS,SAEf,SAASC,EACRC,EACAC,EACAC,EACAC,EACO,CACP,OAAQH,EAAMI,EAAO,CACpB,OACA,OACC,OAAOC,EACNL,EACAC,EACAC,EACAC,CACD,EACD,OACC,OAAOG,EAAqBN,EAAOC,EAAUC,EAASC,CAAc,EACrE,OACC,OAAOI,EACLP,EACDC,EACAC,EACAC,CACD,CACF,CACD,CAEA,SAASG,EACRN,EACAC,EACAC,EACAC,EACC,CACD,GAAI,CAACK,IAAOC,GAAS,EAAIT,EACrBU,EAAQV,EAAMU,EAGdA,EAAM,OAASF,EAAM,SAEvB,CAACA,EAAOE,CAAK,EAAI,CAACA,EAAOF,CAAK,EAC9B,CAACN,EAASC,CAAc,EAAI,CAACA,EAAgBD,CAAO,GAItD,QAASS,EAAI,EAAGA,EAAIH,EAAM,OAAQG,IACjC,GAAIF,EAAUE,CAAC,GAAKD,EAAMC,CAAC,IAAMH,EAAMG,CAAC,EAAG,CAC1C,IAAMC,EAAOX,EAAS,OAAO,CAACU,CAAC,CAAC,EAChCT,EAAQ,KAAK,CACZ,GAAIN,EACJ,KAAAgB,EAGA,MAAOC,EAAwBH,EAAMC,CAAC,CAAC,CACxC,CAAC,EACDR,EAAe,KAAK,CACnB,GAAIP,EACJ,KAAAgB,EACA,MAAOC,EAAwBL,EAAMG,CAAC,CAAC,CACxC,CAAC,EAKH,QAASA,EAAIH,EAAM,OAAQG,EAAID,EAAM,OAAQC,IAAK,CACjD,IAAMC,EAAOX,EAAS,OAAO,CAACU,CAAC,CAAC,EAChCT,EAAQ,KAAK,CACZ,GAAIL,EACJ,KAAAe,EAGA,MAAOC,EAAwBH,EAAMC,CAAC,CAAC,CACxC,CAAC,EAEF,QAASA,EAAID,EAAM,OAAS,EAAGF,EAAM,QAAUG,EAAG,EAAEA,EAAG,CACtD,IAAMC,EAAOX,EAAS,OAAO,CAACU,CAAC,CAAC,EAChCR,EAAe,KAAK,CACnB,GAAIL,EACJ,KAAAc,CACD,CAAC,EAEH,CAGA,SAASP,EACRL,EACAC,EACAC,EACAC,EACC,CACD,GAAM,CAACK,IAAOE,GAAK,EAAIV,EACvBc,EAAKd,EAAMS,EAAY,CAACM,EAAKC,IAAkB,CAC9C,IAAMC,EAAYC,EAAIV,EAAOO,CAAG,EAC1BI,EAAQD,EAAIR,EAAQK,CAAG,EACvBK,EAAMJ,EAAyBK,EAAIb,EAAOO,CAAG,EAAInB,EAAUC,EAArCC,EAC5B,GAAImB,IAAcE,GAASC,IAAOxB,EAAS,OAC3C,IAAMgB,EAAOX,EAAS,OAAOc,CAAU,EACvCb,EAAQ,KAAKkB,IAAOtB,EAAS,CAAC,GAAAsB,EAAI,KAAAR,CAAI,EAAI,CAAC,GAAAQ,EAAI,KAAAR,EAAM,MAAAO,CAAK,CAAC,EAC3DhB,EAAe,KACdiB,IAAOvB,EACJ,CAAC,GAAIC,EAAQ,KAAAc,CAAI,EACjBQ,IAAOtB,EACP,CAAC,GAAID,EAAK,KAAAe,EAAM,MAAOC,EAAwBI,CAAS,CAAC,EACzD,CAAC,GAAIrB,EAAS,KAAAgB,EAAM,MAAOC,EAAwBI,CAAS,CAAC,CACjE,CACD,CAAC,CACF,CAEA,SAASV,EACRP,EACAC,EACAC,EACAC,EACC,CACD,GAAI,CAACK,IAAOE,GAAK,EAAIV,EAEjBW,EAAI,EACRH,EAAM,QAASW,GAAe,CAC7B,GAAI,CAACT,EAAO,IAAIS,CAAK,EAAG,CACvB,IAAMP,EAAOX,EAAS,OAAO,CAACU,CAAC,CAAC,EAChCT,EAAQ,KAAK,CACZ,GAAIJ,EACJ,KAAAc,EACA,MAAAO,CACD,CAAC,EACDhB,EAAe,QAAQ,CACtB,GAAIN,EACJ,KAAAe,EACA,MAAAO,CACD,CAAC,EAEFR,GACD,CAAC,EACDA,EAAI,EACJD,EAAO,QAASS,GAAe,CAC9B,GAAI,CAACX,EAAM,IAAIW,CAAK,EAAG,CACtB,IAAMP,EAAOX,EAAS,OAAO,CAACU,CAAC,CAAC,EAChCT,EAAQ,KAAK,CACZ,GAAIL,EACJ,KAAAe,EACA,MAAAO,CACD,CAAC,EACDhB,EAAe,QAAQ,CACtB,GAAIL,EACJ,KAAAc,EACA,MAAAO,CACD,CAAC,EAEFR,GACD,CAAC,CACF,CAEA,SAASW,EACRC,EACAC,EACAtB,EACAC,EACO,CACPD,EAAQ,KAAK,CACZ,GAAIN,EACJ,KAAM,CAAC,EACP,MAAO4B,IAAgBC,EAAU,OAAYD,CAC9C,CAAC,EACDrB,EAAe,KAAK,CACnB,GAAIP,EACJ,KAAM,CAAC,EACP,MAAO2B,CACR,CAAC,CACF,CAEA,SAASG,EAAiBC,EAAUzB,EAA8B,CACjE,OAAAA,EAAQ,QAAQ0B,GAAS,CACxB,GAAM,CAAC,KAAAhB,EAAM,GAAAQ,CAAE,EAAIQ,EAEfC,EAAYF,EAChB,QAAShB,EAAI,EAAGA,EAAIC,EAAK,OAAS,EAAGD,IAAK,CACzC,IAAMmB,EAAaC,EAAYF,CAAI,EAC/BG,EAAIpB,EAAKD,CAAC,EACV,OAAOqB,GAAM,UAAY,OAAOA,GAAM,WACzCA,EAAI,GAAKA,IAKRF,IAAe,GAAmBA,IAAe,KACjDE,IAAM,aAAeA,IAAM,gBAE5BC,EAAI,GAAc,CAAC,EAChB,OAAOJ,GAAS,YAAcG,IAAM,aACvCC,EAAI,GAAc,CAAC,EACpBJ,EAAOX,EAAIW,EAAMG,CAAC,EACd,OAAOH,GAAS,UAAUI,EAAI,GAAc,EAAGrB,EAAK,KAAK,GAAG,CAAC,EAGlE,IAAMsB,EAAOH,EAAYF,CAAI,EACvBV,EAAQgB,EAAoBP,EAAM,KAAK,EACvCb,EAAMH,EAAKA,EAAK,OAAS,CAAC,EAChC,OAAQQ,EAAI,CACX,KAAKxB,EACJ,OAAQsC,EAAM,CACb,OACC,OAAOL,EAAK,IAAId,EAAKI,CAAK,EAE3B,OACCc,EAAI,EAAW,EAChB,QAKC,OAAQJ,EAAKd,CAAG,EAAII,CACtB,CACD,KAAKtB,EACJ,OAAQqC,EAAM,CACb,OACC,OAAOnB,IAAQ,IACZc,EAAK,KAAKV,CAAK,EACfU,EAAK,OAAOd,EAAY,EAAGI,CAAK,EACpC,OACC,OAAOU,EAAK,IAAId,EAAKI,CAAK,EAC3B,OACC,OAAOU,EAAK,IAAIV,CAAK,EACtB,QACC,OAAQU,EAAKd,CAAG,EAAII,CACtB,CACD,KAAKrB,EACJ,OAAQoC,EAAM,CACb,OACC,OAAOL,EAAK,OAAOd,EAAY,CAAC,EACjC,OACC,OAAOc,EAAK,OAAOd,CAAG,EACvB,OACC,OAAOc,EAAK,OAAOD,EAAM,KAAK,EAC/B,QACC,OAAO,OAAOC,EAAKd,CAAG,CACxB,CACD,QACCkB,EAAI,GAAc,EAAGb,CAAE,CACzB,CACD,CAAC,EAEMO,CACR,CAMA,SAASQ,EAAoBC,EAAU,CACtC,GAAI,CAACC,EAAYD,CAAG,EAAG,OAAOA,EAC9B,GAAI,MAAM,QAAQA,CAAG,EAAG,OAAOA,EAAI,IAAID,CAAmB,EAC1D,GAAIG,EAAMF,CAAG,EACZ,OAAO,IAAI,IACV,MAAM,KAAKA,EAAI,QAAQ,CAAC,EAAE,IAAI,CAAC,CAACG,EAAGC,CAAC,IAAM,CAACD,EAAGJ,EAAoBK,CAAC,CAAC,CAAC,CACtE,EACD,GAAIC,EAAML,CAAG,EAAG,OAAO,IAAI,IAAI,MAAM,KAAKA,CAAG,EAAE,IAAID,CAAmB,CAAC,EACvE,IAAMO,EAAS,OAAO,OAAOC,EAAeP,CAAG,CAAC,EAChD,QAAWrB,KAAOqB,EAAKM,EAAO3B,CAAG,EAAIoB,EAAoBC,EAAIrB,CAAG,CAAC,EACjE,OAAIM,EAAIe,EAAKQ,CAAS,IAAGF,EAAOE,CAAS,EAAIR,EAAIQ,CAAS,GACnDF,CACR,CAEA,SAAS7B,EAA2BuB,EAAW,CAC9C,OAAIS,EAAQT,CAAG,EACPD,EAAoBC,CAAG,EACjBA,CACf,CAEAU,EAAW,UAAW,CACrBpB,IACA3B,IACAuB,GACD,CAAC,CACF,CCzSO,SAASyB,IAAe,CAC9B,MAAMC,UAAiB,GAAI,CAG1B,YAAYC,EAAgBC,EAAqB,CAChD,MAAM,EACN,KAAKC,CAAW,EAAI,CACnBC,IACAC,EAASH,EACTI,EAAQJ,EAASA,EAAOI,EAASC,EAAgB,EACjDC,EAAW,GACXC,EAAY,GACZC,EAAO,OACPC,EAAW,OACXC,EAAOX,EACPY,EAAQ,KACRC,EAAW,GACXC,EAAU,EACX,CACD,CAEA,IAAI,MAAe,CAClB,OAAOC,EAAO,KAAKb,CAAW,CAAC,EAAE,IAClC,CAEA,IAAIc,EAAmB,CACtB,OAAOD,EAAO,KAAKb,CAAW,CAAC,EAAE,IAAIc,CAAG,CACzC,CAEA,IAAIA,EAAUC,EAAY,CACzB,IAAMC,EAAkB,KAAKhB,CAAW,EACxC,OAAAiB,EAAgBD,CAAK,GACjB,CAACH,EAAOG,CAAK,EAAE,IAAIF,CAAG,GAAKD,EAAOG,CAAK,EAAE,IAAIF,CAAG,IAAMC,KACzDG,EAAeF,CAAK,EACpBG,EAAYH,CAAK,EACjBA,EAAMR,EAAW,IAAIM,EAAK,EAAI,EAC9BE,EAAMT,EAAO,IAAIO,EAAKC,CAAK,EAC3BC,EAAMR,EAAW,IAAIM,EAAK,EAAI,GAExB,IACR,CAEA,OAAOA,EAAmB,CACzB,GAAI,CAAC,KAAK,IAAIA,CAAG,EAChB,MAAO,GAGR,IAAME,EAAkB,KAAKhB,CAAW,EACxC,OAAAiB,EAAgBD,CAAK,EACrBE,EAAeF,CAAK,EACpBG,EAAYH,CAAK,EACbA,EAAMP,EAAM,IAAIK,CAAG,EACtBE,EAAMR,EAAW,IAAIM,EAAK,EAAK,EAE/BE,EAAMR,EAAW,OAAOM,CAAG,EAE5BE,EAAMT,EAAO,OAAOO,CAAG,EAChB,EACR,CAEA,OAAQ,CACP,IAAME,EAAkB,KAAKhB,CAAW,EACxCiB,EAAgBD,CAAK,EACjBH,EAAOG,CAAK,EAAE,OACjBE,EAAeF,CAAK,EACpBG,EAAYH,CAAK,EACjBA,EAAMR,EAAY,IAAI,IACtBY,EAAKJ,EAAMP,EAAOK,GAAO,CACxBE,EAAMR,EAAW,IAAIM,EAAK,EAAK,CAChC,CAAC,EACDE,EAAMT,EAAO,MAAM,EAErB,CAEA,QAAQc,EAA+CC,EAAe,CACrE,IAAMN,EAAkB,KAAKhB,CAAW,EACxCa,EAAOG,CAAK,EAAE,QAAQ,CAACO,EAAaT,EAAUU,IAAc,CAC3DH,EAAG,KAAKC,EAAS,KAAK,IAAIR,CAAG,EAAGA,EAAK,IAAI,CAC1C,CAAC,CACF,CAEA,IAAIA,EAAe,CAClB,IAAME,EAAkB,KAAKhB,CAAW,EACxCiB,EAAgBD,CAAK,EACrB,IAAMD,EAAQF,EAAOG,CAAK,EAAE,IAAIF,CAAG,EAInC,GAHIE,EAAMV,GAAc,CAACmB,EAAYV,CAAK,GAGtCA,IAAUC,EAAMP,EAAM,IAAIK,CAAG,EAChC,OAAOC,EAGR,IAAMW,EAAQC,EAAYZ,EAAOC,CAAK,EACtC,OAAAE,EAAeF,CAAK,EACpBA,EAAMT,EAAO,IAAIO,EAAKY,CAAK,EACpBA,CACR,CAEA,MAA8B,CAC7B,OAAOb,EAAO,KAAKb,CAAW,CAAC,EAAE,KAAK,CACvC,CAEA,QAAgC,CAC/B,IAAM4B,EAAW,KAAK,KAAK,EAC3B,MAAO,CACN,CAAC,OAAO,QAAQ,EAAG,IAAM,KAAK,OAAO,EACrC,KAAM,IAAM,CACX,IAAMC,EAAID,EAAS,KAAK,EAExB,OAAIC,EAAE,KAAaA,EAEZ,CACN,KAAM,GACN,MAHa,KAAK,IAAIA,EAAE,KAAK,CAI9B,CACD,CACD,CACD,CAEA,SAAwC,CACvC,IAAMD,EAAW,KAAK,KAAK,EAC3B,MAAO,CACN,CAAC,OAAO,QAAQ,EAAG,IAAM,KAAK,QAAQ,EACtC,KAAM,IAAM,CACX,IAAMC,EAAID,EAAS,KAAK,EAExB,GAAIC,EAAE,KAAM,OAAOA,EACnB,IAAMd,EAAQ,KAAK,IAAIc,EAAE,KAAK,EAC9B,MAAO,CACN,KAAM,GACN,MAAO,CAACA,EAAE,MAAOd,CAAK,CACvB,CACD,CACD,CACD,CAEA,EAtICf,EAsIA,OAAO,SAAQ,GAAI,CACnB,OAAO,KAAK,QAAQ,CACrB,CACD,CAEA,SAAS8B,EAA4BhC,EAAWC,EAAwB,CAEvE,OAAO,IAAIF,EAASC,EAAQC,CAAM,CACnC,CAEA,SAASmB,EAAeF,EAAiB,CACnCA,EAAMT,IACVS,EAAMR,EAAY,IAAI,IACtBQ,EAAMT,EAAQ,IAAI,IAAIS,EAAMP,CAAK,EAEnC,CAEA,MAAMsB,UAAiB,GAAI,CAE1B,YAAYjC,EAAgBC,EAAqB,CAChD,MAAM,EACN,KAAKC,CAAW,EAAI,CACnBC,IACAC,EAASH,EACTI,EAAQJ,EAASA,EAAOI,EAASC,EAAgB,EACjDC,EAAW,GACXC,EAAY,GACZC,EAAO,OACPE,EAAOX,EACPY,EAAQ,KACRsB,EAAS,IAAI,IACbpB,EAAU,GACVD,EAAW,EACZ,CACD,CAEA,IAAI,MAAe,CAClB,OAAOE,EAAO,KAAKb,CAAW,CAAC,EAAE,IAClC,CAEA,IAAIe,EAAqB,CACxB,IAAMC,EAAkB,KAAKhB,CAAW,EAGxC,OAFAiB,EAAgBD,CAAK,EAEhBA,EAAMT,EAGP,GAAAS,EAAMT,EAAM,IAAIQ,CAAK,GACrBC,EAAMgB,EAAQ,IAAIjB,CAAK,GAAKC,EAAMT,EAAM,IAAIS,EAAMgB,EAAQ,IAAIjB,CAAK,CAAC,GAHhEC,EAAMP,EAAM,IAAIM,CAAK,CAM9B,CAEA,IAAIA,EAAiB,CACpB,IAAMC,EAAkB,KAAKhB,CAAW,EACxC,OAAAiB,EAAgBD,CAAK,EAChB,KAAK,IAAID,CAAK,IAClBkB,EAAejB,CAAK,EACpBG,EAAYH,CAAK,EACjBA,EAAMT,EAAO,IAAIQ,CAAK,GAEhB,IACR,CAEA,OAAOA,EAAiB,CACvB,GAAI,CAAC,KAAK,IAAIA,CAAK,EAClB,MAAO,GAGR,IAAMC,EAAkB,KAAKhB,CAAW,EACxC,OAAAiB,EAAgBD,CAAK,EACrBiB,EAAejB,CAAK,EACpBG,EAAYH,CAAK,EAEhBA,EAAMT,EAAO,OAAOQ,CAAK,IACxBC,EAAMgB,EAAQ,IAAIjB,CAAK,EACrBC,EAAMT,EAAO,OAAOS,EAAMgB,EAAQ,IAAIjB,CAAK,CAAC,EACjB,GAEhC,CAEA,OAAQ,CACP,IAAMC,EAAkB,KAAKhB,CAAW,EACxCiB,EAAgBD,CAAK,EACjBH,EAAOG,CAAK,EAAE,OACjBiB,EAAejB,CAAK,EACpBG,EAAYH,CAAK,EACjBA,EAAMT,EAAO,MAAM,EAErB,CAEA,QAAgC,CAC/B,IAAMS,EAAkB,KAAKhB,CAAW,EACxC,OAAAiB,EAAgBD,CAAK,EACrBiB,EAAejB,CAAK,EACbA,EAAMT,EAAO,OAAO,CAC5B,CAEA,SAAwC,CACvC,IAAMS,EAAkB,KAAKhB,CAAW,EACxC,OAAAiB,EAAgBD,CAAK,EACrBiB,EAAejB,CAAK,EACbA,EAAMT,EAAO,QAAQ,CAC7B,CAEA,MAA8B,CAC7B,OAAO,KAAK,OAAO,CACpB,CAEA,EA3FCP,EA2FA,OAAO,SAAQ,GAAI,CACnB,OAAO,KAAK,OAAO,CACpB,CAEA,QAAQqB,EAASC,EAAe,CAC/B,IAAMM,EAAW,KAAK,OAAO,EACzBM,EAASN,EAAS,KAAK,EAC3B,KAAO,CAACM,EAAO,MACdb,EAAG,KAAKC,EAASY,EAAO,MAAOA,EAAO,MAAO,IAAI,EACjDA,EAASN,EAAS,KAAK,CAEzB,CACD,CACA,SAASO,EAA4BrC,EAAWC,EAAwB,CAEvE,OAAO,IAAIgC,EAASjC,EAAQC,CAAM,CACnC,CAEA,SAASkC,EAAejB,EAAiB,CACnCA,EAAMT,IAEVS,EAAMT,EAAQ,IAAI,IAClBS,EAAMP,EAAM,QAAQM,GAAS,CAC5B,GAAIU,EAAYV,CAAK,EAAG,CACvB,IAAMW,EAAQC,EAAYZ,EAAOC,CAAK,EACtCA,EAAMgB,EAAQ,IAAIjB,EAAOW,CAAK,EAC9BV,EAAMT,EAAO,IAAImB,CAAK,OAEtBV,EAAMT,EAAO,IAAIQ,CAAK,CAExB,CAAC,EAEH,CAEA,SAASE,EAAgBD,EAA+C,CACnEA,EAAMJ,GAAUwB,EAAI,EAAG,KAAK,UAAUvB,EAAOG,CAAK,CAAC,CAAC,CACzD,CAEAqB,EAAW,SAAU,CAACP,IAAWK,GAAS,CAAC,CAC5C,CXrRA,IAAMG,EAAQ,IAAIC,EAqBLC,GAAoBF,EAAM,QAM1BG,GAA0CH,EAAM,mBAAmB,KAC/EA,CACD,EAOaI,GAAgBJ,EAAM,cAAc,KAAKA,CAAK,EAO9CK,GAA0BL,EAAM,wBAAwB,KAAKA,CAAK,EAOlEM,GAAeN,EAAM,aAAa,KAAKA,CAAK,EAM5CO,GAAcP,EAAM,YAAY,KAAKA,CAAK,EAU1CQ,GAAcR,EAAM,YAAY,KAAKA,CAAK,EAQhD,SAASS,GAAaC,EAAoB,CAChD,OAAOA,CACR,CAOO,SAASC,GAAiBD,EAAwB,CACxD,OAAOA,CACR", "names": ["immer_exports", "__export", "Immer", "applyPatches", "castDraft", "castImmutable", "createDraft", "current", "enableMapSet", "enablePatches", "finishDraft", "freeze", "DRAFTABLE", "isDraft", "isDraftable", "NOTHING", "original", "produce", "produceWithPatches", "setAutoFreeze", "setUseStrictShallowCopy", "__toCommonJS", "NOTHING", "DRAFTABLE", "DRAFT_STATE", "die", "error", "args", "getPrototypeOf", "isDraft", "value", "DRAFT_STATE", "isDraftable", "isPlainObject", "DRAFTABLE", "isMap", "isSet", "objectCtorString", "proto", "Ctor", "original", "die", "base_", "each", "obj", "iter", "getArchtype", "key", "entry", "index", "thing", "state", "type_", "has", "prop", "get", "set", "propOrOldValue", "t", "is", "x", "y", "target", "latest", "copy_", "shallowCopy", "base", "strict", "<PERSON><PERSON><PERSON>", "descriptors", "keys", "i", "desc", "freeze", "deep", "isFrozen", "dontMutateFrozenCollections", "plugins", "getPlugin", "pluginKey", "plugin", "die", "loadPlugin", "implementation", "currentScope", "getCurrentScope", "createScope", "parent_", "immer_", "drafts_", "canAutoFreeze_", "unfinalizedDrafts_", "usePatchesInScope", "scope", "patchListener", "getPlugin", "patches_", "inversePatches_", "patchListener_", "revokeScope", "leaveScope", "revokeDraft", "enterScope", "immer", "draft", "state", "DRAFT_STATE", "type_", "revoke_", "revoked_", "processResult", "result", "scope", "unfinalizedDrafts_", "drafts_", "baseDraft", "DRAFT_STATE", "modified_", "revokeScope", "die", "isDraftable", "finalize", "parent_", "<PERSON><PERSON><PERSON><PERSON>", "patches_", "getPlugin", "generateReplacementPatches_", "base_", "inversePatches_", "patchListener_", "NOTHING", "rootScope", "value", "path", "isFrozen", "state", "each", "key", "childValue", "finalizeProperty", "scope_", "finalized_", "copy_", "resultEach", "isSet", "type_", "generatePatches_", "parentState", "targetObject", "prop", "rootPath", "targetIsSet", "isDraft", "has", "assigned_", "res", "set", "canAutoFreeze_", "immer_", "autoFreeze_", "deep", "freeze", "createProxyProxy", "base", "parent", "isArray", "state", "type_", "scope_", "getCurrentScope", "modified_", "finalized_", "assigned_", "parent_", "base_", "draft_", "copy_", "revoke_", "isManual_", "target", "traps", "objectTraps", "arrayTraps", "revoke", "proxy", "prop", "DRAFT_STATE", "source", "latest", "has", "readPropFromProto", "value", "isDraftable", "peek", "prepareCopy", "createProxy", "desc", "getDescriptorFromProto", "current", "currentState", "is", "<PERSON><PERSON><PERSON><PERSON>", "owner", "die", "getPrototypeOf", "each", "key", "fn", "draft", "proto", "shallowCopy", "immer_", "useStrictShallowCopy_", "Immer", "config", "autoFreeze_", "useStrictShallowCopy_", "base", "recipe", "patchListener", "defaultBase", "self", "args", "draft", "die", "result", "isDraftable", "scope", "enterScope", "proxy", "createProxy", "<PERSON><PERSON><PERSON><PERSON>", "revokeScope", "leaveScope", "usePatchesInScope", "processResult", "NOTHING", "freeze", "p", "ip", "getPlugin", "generateReplacementPatches_", "state", "patches", "inversePatches", "isDraft", "current", "DRAFT_STATE", "isManual_", "scope_", "value", "i", "patch", "applyPatchesImpl", "applyPatches_", "parent", "isMap", "proxyMap_", "isSet", "proxySet_", "createProxyProxy", "getCurrentScope", "drafts_", "current", "value", "isDraft", "die", "currentImpl", "isDraftable", "isFrozen", "state", "DRAFT_STATE", "copy", "modified_", "base_", "finalized_", "shallowCopy", "scope_", "immer_", "useStrictShallowCopy_", "each", "key", "childValue", "set", "enablePatches", "REPLACE", "ADD", "REMOVE", "generatePatches_", "state", "basePath", "patches", "inversePatches", "type_", "generatePatchesFromAssigned", "generateArrayPatches", "generateSetPatches", "base_", "assigned_", "copy_", "i", "path", "clonePatchValueIfNeeded", "each", "key", "assignedValue", "origValue", "get", "value", "op", "has", "generateReplacementPatches_", "baseValue", "replacement", "NOTHING", "applyPatches_", "draft", "patch", "base", "parentType", "getArchtype", "p", "die", "type", "deepClonePatchValue", "obj", "isDraftable", "isMap", "k", "v", "isSet", "cloned", "getPrototypeOf", "DRAFTABLE", "isDraft", "loadPlugin", "enableMapSet", "DraftMap", "target", "parent", "DRAFT_STATE", "type_", "parent_", "scope_", "getCurrentScope", "modified_", "finalized_", "copy_", "assigned_", "base_", "draft_", "isManual_", "revoked_", "latest", "key", "value", "state", "assertUnrevoked", "prepareMapCopy", "<PERSON><PERSON><PERSON><PERSON>", "each", "cb", "thisArg", "_value", "_map", "isDraftable", "draft", "createProxy", "iterator", "r", "proxyMap_", "DraftSet", "drafts_", "prepareSetCopy", "result", "proxySet_", "die", "loadPlugin", "immer", "Immer", "produce", "produceWithPatches", "setAutoFreeze", "setUseStrictShallowCopy", "applyPatches", "createDraft", "finishDraft", "castDraft", "value", "castImmutable"]}