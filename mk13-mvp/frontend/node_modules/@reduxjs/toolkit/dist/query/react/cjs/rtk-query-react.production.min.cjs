"use strict";var Pe=Object.create;var W=Object.defineProperty;var he=Object.getOwnPropertyDescriptor;var Ie=Object.getOwnPropertyNames;var Ue=Object.getPrototypeOf,be=Object.prototype.hasOwnProperty;var Ee=(e,s)=>{for(var d in s)W(e,d,{get:s[d],enumerable:!0})},j=(e,s,d,R)=>{if(s&&typeof s=="object"||typeof s=="function")for(let T of Ie(s))!be.call(e,T)&&T!==d&&W(e,T,{get:()=>s[T],enumerable:!(R=he(s,T))||R.enumerable});return e},k=(e,s,d)=>(j(e,s,"default"),d&&j(d,s,"default")),ke=(e,s,d)=>(d=e!=null?Pe(Ue(e)):{},j(s||!e||!e.__esModule?W(d,"default",{value:e,enumerable:!0}):d,e)),Me=e=>j(W({},"__esModule",{value:!0}),e);var E={};Ee(E,{ApiProvider:()=>Se,UNINITIALIZED_VALUE:()=>H,createApi:()=>Ne,reactHooksModule:()=>Qe,reactHooksModuleName:()=>fe});module.exports=Me(E);var te=require("@reduxjs/toolkit/query");var Xe=require("@reduxjs/toolkit"),M=require("react-redux"),Te=require("reselect");function V(e){return e.replace(e[0],e[0].toUpperCase())}function de(e){return e.type==="query"}function ce(e){return e.type==="mutation"}function $(e){return e.type==="infinitequery"}function N(e,...s){return Object.assign(e,...s)}var ge=require("@reduxjs/toolkit"),A=require("@reduxjs/toolkit/query"),n=require("react"),Y=require("react-redux");var H=Symbol();var z=require("react");function G(e,s,d,R){let T=(0,z.useMemo)(()=>({queryArgs:e,serialized:typeof e=="object"?s({queryArgs:e,endpointDefinition:d,endpointName:R}):e}),[e,s,d,R]),D=(0,z.useRef)(T);return(0,z.useEffect)(()=>{D.current.serialized!==T.serialized&&(D.current=T)},[T]),D.current.serialized===T.serialized?D.current.queryArgs:e}var Z=require("react"),ye=require("react-redux");function _(e){let s=(0,Z.useRef)(e);return(0,Z.useEffect)(()=>{(0,ye.shallowEqual)(s.current,e)||(s.current=e)},[e]),(0,ye.shallowEqual)(s.current,e)?s.current:e}var Oe=()=>typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Fe=Oe(),we=()=>typeof navigator<"u"&&navigator.product==="ReactNative",ve=we(),Le=()=>Fe||ve?n.useLayoutEffect:n.useEffect,Ce=Le(),le=e=>e.isUninitialized?{...e,isUninitialized:!1,isFetching:!0,isLoading:e.data===void 0,status:A.QueryStatus.pending}:e;function oe(e,...s){let d={};return s.forEach(R=>{d[R]=e[R]}),d}var pe=["data","status","isLoading","isSuccess","isError","error"];function Re({api:e,moduleOptions:{batch:s,hooks:{useDispatch:d,useSelector:R,useStore:T},unstable__sideEffectsInRender:D,createSelector:L},serializeQueryArgs:U,context:h}){let O=D?t=>t():n.useEffect;return{buildQueryHooks:q,buildInfiniteQueryHooks:se,buildMutationHook:ae,usePrefetch:ie};function ne(t,a,Q){if(a?.endpointName&&t.isUninitialized){let{endpointName:y}=a,p=h.endpointDefinitions[y];Q!==A.skipToken&&U({queryArgs:a.originalArgs,endpointDefinition:p,endpointName:y})===U({queryArgs:Q,endpointDefinition:p,endpointName:y})&&(a=void 0)}let o=t.isSuccess?t.data:a?.data;o===void 0&&(o=t.data);let u=o!==void 0,r=t.isLoading,i=(!a||a.isLoading||a.isUninitialized)&&!u&&r,f=t.isSuccess||u&&(r&&!a?.isError||t.isUninitialized);return{...t,data:o,currentData:t.data,isFetching:r,isLoading:i,isSuccess:f}}function re(t,a,Q){if(a?.endpointName&&t.isUninitialized){let{endpointName:y}=a,p=h.endpointDefinitions[y];Q!==A.skipToken&&U({queryArgs:a.originalArgs,endpointDefinition:p,endpointName:y})===U({queryArgs:Q,endpointDefinition:p,endpointName:y})&&(a=void 0)}let o=t.isSuccess?t.data:a?.data;o===void 0&&(o=t.data);let u=o!==void 0,r=t.isLoading,i=(!a||a.isLoading||a.isUninitialized)&&!u&&r,f=t.isSuccess||r&&u;return{...t,data:o,currentData:t.data,isFetching:r,isLoading:i,isSuccess:f}}function ie(t,a){let Q=d(),o=_(a);return(0,n.useCallback)((u,r)=>Q(e.util.prefetch(t,u,{...o,...r})),[t,Q,o])}function B(t,a,{refetchOnReconnect:Q,refetchOnFocus:o,refetchOnMountOrArgChange:u,skip:r=!1,pollingInterval:i=0,skipPollingIfUnfocused:f=!1,...y}={}){let{initiate:p}=e.endpoints[t],l=d(),P=(0,n.useRef)(void 0);if(!P.current){let v=l(e.internalActions.internal_getRTKQSubscriptions());P.current=v}let c=G(r?A.skipToken:a,A.defaultSerializeQueryArgs,h.endpointDefinitions[t],t),g=_({refetchOnReconnect:Q,refetchOnFocus:o,pollingInterval:i,skipPollingIfUnfocused:f}),x=y.initialPageParam,m=_(x),S=(0,n.useRef)(void 0),{queryCacheKey:b,requestId:w}=S.current||{},K=!1;b&&w&&(K=P.current.isRequestSubscribed(b,w));let ue=!K&&S.current!==void 0;return O(()=>{ue&&(S.current=void 0)},[ue]),O(()=>{let v=S.current;if(typeof process<"u",c===A.skipToken){v?.unsubscribe(),S.current=void 0;return}let Ae=S.current?.subscriptionOptions;if(!v||v.arg!==c){v?.unsubscribe();let Be=l(p(c,{subscriptionOptions:g,forceRefetch:u,...$(h.endpointDefinitions[t])?{initialPageParam:m}:{}}));S.current=Be}else g!==Ae&&v.updateSubscriptionOptions(g)},[l,p,u,c,g,ue,m,t]),[S,l,p,g]}function C(t,a){return(o,{skip:u=!1,selectFromResult:r}={})=>{let{select:i}=e.endpoints[t],f=G(u?A.skipToken:o,U,h.endpointDefinitions[t],t),y=(0,n.useRef)(void 0),p=(0,n.useMemo)(()=>L([i(f),(x,m)=>m,x=>f],a,{memoizeOptions:{resultEqualityCheck:Y.shallowEqual}}),[i,f]),l=(0,n.useMemo)(()=>r?L([p],r,{devModeChecks:{identityFunctionCheck:"never"}}):p,[p,r]),P=R(x=>l(x,y.current),Y.shallowEqual),c=T(),g=p(c.getState(),y.current);return Ce(()=>{y.current=g},[g]),P}}function I(t){(0,n.useEffect)(()=>()=>{t.current?.unsubscribe?.(),t.current=void 0},[t])}function F(t){if(!t.current)throw new Error((0,ge.formatProdErrorMessage)(38));return t.current.refetch()}function q(t){let a=(u,r={})=>{let[i]=B(t,u,r);return I(i),(0,n.useMemo)(()=>({refetch:()=>F(i)}),[i])},Q=({refetchOnReconnect:u,refetchOnFocus:r,pollingInterval:i=0,skipPollingIfUnfocused:f=!1}={})=>{let{initiate:y}=e.endpoints[t],p=d(),[l,P]=(0,n.useState)(H),c=(0,n.useRef)(void 0),g=_({refetchOnReconnect:u,refetchOnFocus:r,pollingInterval:i,skipPollingIfUnfocused:f});O(()=>{let b=c.current?.subscriptionOptions;g!==b&&c.current?.updateSubscriptionOptions(g)},[g]);let x=(0,n.useRef)(g);O(()=>{x.current=g},[g]);let m=(0,n.useCallback)(function(b,w=!1){let K;return s(()=>{c.current?.unsubscribe(),c.current=K=p(y(b,{subscriptionOptions:x.current,forceRefetch:!w})),P(b)}),K},[p,y]),S=(0,n.useCallback)(()=>{c.current?.queryCacheKey&&p(e.internalActions.removeQueryResult({queryCacheKey:c.current?.queryCacheKey}))},[p]);return(0,n.useEffect)(()=>()=>{c?.current?.unsubscribe()},[]),(0,n.useEffect)(()=>{l!==H&&!c.current&&m(l,!0)},[l,m]),(0,n.useMemo)(()=>[m,l,{reset:S}],[m,l,S])},o=C(t,ne);return{useQueryState:o,useQuerySubscription:a,useLazyQuerySubscription:Q,useLazyQuery(u){let[r,i,{reset:f}]=Q(u),y=o(i,{...u,skip:i===H}),p=(0,n.useMemo)(()=>({lastArg:i}),[i]);return(0,n.useMemo)(()=>[r,{...y,reset:f},p],[r,y,f,p])},useQuery(u,r){let i=a(u,r),f=o(u,{selectFromResult:u===A.skipToken||r?.skip?void 0:le,...r}),y=oe(f,...pe);return(0,n.useDebugValue)(y),(0,n.useMemo)(()=>({...f,...i}),[f,i])}}}function se(t){let a=(o,u={})=>{let[r,i,f,y]=B(t,o,u),p=(0,n.useRef)(y);O(()=>{p.current=y},[y]);let l=(0,n.useCallback)(function(g,x){let m;return s(()=>{r.current?.unsubscribe(),r.current=m=i(f(g,{subscriptionOptions:p.current,direction:x}))}),m},[r,i,f]);I(r);let P=G(u.skip?A.skipToken:o,A.defaultSerializeQueryArgs,h.endpointDefinitions[t],t),c=(0,n.useCallback)(()=>F(r),[r]);return(0,n.useMemo)(()=>({trigger:l,refetch:c,fetchNextPage:()=>l(P,"forward"),fetchPreviousPage:()=>l(P,"backward")}),[c,l,P])},Q=C(t,re);return{useInfiniteQueryState:Q,useInfiniteQuerySubscription:a,useInfiniteQuery(o,u){let{refetch:r,fetchNextPage:i,fetchPreviousPage:f}=a(o,u),y=Q(o,{selectFromResult:o===A.skipToken||u?.skip?void 0:le,...u}),p=oe(y,...pe,"hasNextPage","hasPreviousPage");return(0,n.useDebugValue)(p),(0,n.useMemo)(()=>({...y,fetchNextPage:i,fetchPreviousPage:f,refetch:r}),[y,i,f,r])}}}function ae(t){return({selectFromResult:a,fixedCacheKey:Q}={})=>{let{select:o,initiate:u}=e.endpoints[t],r=d(),[i,f]=(0,n.useState)();(0,n.useEffect)(()=>()=>{i?.arg.fixedCacheKey||i?.reset()},[i]);let y=(0,n.useCallback)(function(b){let w=r(u(b,{fixedCacheKey:Q}));return f(w),w},[r,u,Q]),{requestId:p}=i||{},l=(0,n.useMemo)(()=>o({fixedCacheKey:Q,requestId:i?.requestId}),[Q,i,o]),P=(0,n.useMemo)(()=>a?L([l],a):l,[a,l]),c=R(P,Y.shallowEqual),g=Q==null?i?.arg.originalArgs:void 0,x=(0,n.useCallback)(()=>{s(()=>{i&&f(void 0),Q&&r(e.internalActions.removeMutationResult({requestId:p,fixedCacheKey:Q}))})},[r,Q,i,p]),m=oe(c,...pe,"endpointName");(0,n.useDebugValue)(m);let S=(0,n.useMemo)(()=>({...c,originalArgs:g,reset:x}),[c,g,x]);return(0,n.useMemo)(()=>[y,S],[y,S])}}}var fe=Symbol(),Qe=({batch:e=M.batch,hooks:s={useDispatch:M.useDispatch,useSelector:M.useSelector,useStore:M.useStore},createSelector:d=Te.createSelector,unstable__sideEffectsInRender:R=!1,...T}={})=>({name:fe,init(D,{serializeQueryArgs:L},U){let h=D,{buildQueryHooks:O,buildInfiniteQueryHooks:ne,buildMutationHook:re,usePrefetch:ie}=Re({api:D,moduleOptions:{batch:e,hooks:s,unstable__sideEffectsInRender:R,createSelector:d},serializeQueryArgs:L,context:U});return N(h,{usePrefetch:ie}),N(U,{batch:e}),{injectEndpoint(B,C){if(de(C)){let{useQuery:I,useLazyQuery:F,useLazyQuerySubscription:q,useQueryState:se,useQuerySubscription:ae}=O(B);N(h.endpoints[B],{useQuery:I,useLazyQuery:F,useLazyQuerySubscription:q,useQueryState:se,useQuerySubscription:ae}),D[`use${V(B)}Query`]=I,D[`useLazy${V(B)}Query`]=F}if(ce(C)){let I=re(B);N(h.endpoints[B],{useMutation:I}),D[`use${V(B)}Mutation`]=I}else if($(C)){let{useInfiniteQuery:I,useInfiniteQuerySubscription:F,useInfiniteQueryState:q}=ne(B);N(h.endpoints[B],{useInfiniteQuery:I,useInfiniteQuerySubscription:F,useInfiniteQueryState:q}),D[`use${V(B)}InfiniteQuery`]=I}}}}});k(E,require("@reduxjs/toolkit/query"),module.exports);var J=require("@reduxjs/toolkit"),De=require("react"),xe=require("react"),X=ke(require("react")),ee=require("react-redux"),me=require("@reduxjs/toolkit/query");function Se(e){let s=e.context||ee.ReactReduxContext;if((0,De.useContext)(s))throw new Error((0,J.formatProdErrorMessage)(35));let[R]=X.useState(()=>(0,J.configureStore)({reducer:{[e.api.reducerPath]:e.api.reducer},middleware:T=>T().concat(e.api.middleware)}));return(0,xe.useEffect)(()=>e.setupListeners===!1?void 0:(0,me.setupListeners)(R.dispatch,e.setupListeners),[e.setupListeners,R.dispatch]),X.createElement(ee.Provider,{store:R,context:s},e.children)}var Ne=(0,te.buildCreateApi)((0,te.coreModule)(),Qe());0&&(module.exports={ApiProvider,UNINITIALIZED_VALUE,createApi,reactHooksModule,reactHooksModuleName,...require("@reduxjs/toolkit/query")});
//# sourceMappingURL=rtk-query-react.production.min.cjs.map