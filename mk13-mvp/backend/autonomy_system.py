"""
Confidence-Based Autonomy System for MK13 MVP
Learning mechanism with confidence levels for autonomous decision making
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Tuple, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import hashlib
from database import get_managers
from llm_pool import TaskType, get_llm_pool

logger = logging.getLogger(__name__)

class AutonomyLevel(Enum):
    """Autonomy levels based on confidence"""
    QUESTION = "question"           # 0-20%: Ask user before action
    CONFIRMATION = "confirmation"   # 20-60%: Confirm before action
    SILENT = "silent"              # 60-90%: Execute with logging
    AUTONOMOUS = "autonomous"       # 90%+: Execute in background

class ActionType(Enum):
    """Types of actions the system can take"""
    EMAIL_RESPONSE = "email_response"
    CALENDAR_UPDATE = "calendar_update"
    DOCUMENT_CREATION = "document_creation"
    TASK_MANAGEMENT = "task_management"
    CONTEXT_SWITCH = "context_switch"
    DATA_ANALYSIS = "data_analysis"
    MEETING_PREPARATION = "meeting_preparation"
    RESEARCH = "research"
    NOTIFICATION = "notification"
    WORKFLOW_AUTOMATION = "workflow_automation"

@dataclass
class ActionPattern:
    """Represents a learned action pattern"""
    pattern_id: str
    action_type: ActionType
    conditions: Dict[str, Any]
    action_template: Dict[str, Any]
    confidence: float
    success_count: int = 0
    failure_count: int = 0
    total_attempts: int = 0
    user_feedback_score: float = 0.0  # -1 to 1
    last_used: Optional[datetime] = None
    created_at: datetime = field(default_factory=datetime.now)

@dataclass
class PendingAction:
    """Represents an action waiting for user confirmation"""
    action_id: str
    user_id: str
    action_type: ActionType
    action_data: Dict[str, Any]
    confidence: float
    autonomy_level: AutonomyLevel
    reasoning: str
    created_at: datetime = field(default_factory=datetime.now)
    expires_at: datetime = field(default_factory=lambda: datetime.now() + timedelta(hours=1))

class ConfidenceBasedAutonomy:
    """Manages confidence-based autonomous decision making"""
    
    def __init__(self):
        self.action_patterns = {}
        self.pending_actions = {}
        self.user_preferences = {}
        self.confidence_thresholds = {
            AutonomyLevel.QUESTION: (0.0, 0.2),
            AutonomyLevel.CONFIRMATION: (0.2, 0.6),
            AutonomyLevel.SILENT: (0.6, 0.9),
            AutonomyLevel.AUTONOMOUS: (0.9, 1.0)
        }
        self.learning_rate = 0.1
        
        self._initialize_default_patterns()
    
    def _initialize_default_patterns(self):
        """Initialize default action patterns"""
        
        # Email response patterns
        self.action_patterns["auto_email_ack"] = ActionPattern(
            pattern_id="auto_email_ack",
            action_type=ActionType.EMAIL_RESPONSE,
            conditions={
                "sender_type": "internal",
                "email_type": "information",
                "urgency": "low",
                "requires_action": False
            },
            action_template={
                "response_type": "acknowledgment",
                "template": "Thank you for the information. I'll review this and get back to you if I have any questions."
            },
            confidence=0.7
        )
        
        # Meeting preparation patterns
        self.action_patterns["auto_meeting_prep"] = ActionPattern(
            pattern_id="auto_meeting_prep",
            action_type=ActionType.MEETING_PREPARATION,
            conditions={
                "meeting_type": "recurring",
                "participants_known": True,
                "agenda_template_exists": True
            },
            action_template={
                "actions": ["gather_participant_info", "prepare_agenda", "collect_documents"]
            },
            confidence=0.8
        )
        
        # Context switching patterns
        self.action_patterns["auto_context_switch"] = ActionPattern(
            pattern_id="auto_context_switch",
            action_type=ActionType.CONTEXT_SWITCH,
            conditions={
                "calendar_event_starting": True,
                "context_exists": True,
                "current_task_interruptible": True
            },
            action_template={
                "switch_type": "calendar_triggered",
                "save_current_state": True
            },
            confidence=0.6
        )
    
    async def evaluate_action(
        self, 
        user_id: str, 
        action_type: ActionType, 
        action_data: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Evaluate whether to take an action and at what autonomy level"""
        
        context = context or {}
        
        # Find matching patterns
        matching_patterns = self._find_matching_patterns(action_type, action_data, context)
        
        if not matching_patterns:
            # No patterns found, default to asking user
            return {
                "autonomy_level": AutonomyLevel.QUESTION,
                "confidence": 0.1,
                "reasoning": "No learned patterns for this action type",
                "should_execute": False,
                "requires_user_input": True
            }
        
        # Calculate weighted confidence
        total_confidence = 0.0
        total_weight = 0.0
        
        for pattern in matching_patterns:
            weight = self._calculate_pattern_weight(pattern)
            total_confidence += pattern.confidence * weight
            total_weight += weight
        
        if total_weight == 0:
            final_confidence = 0.1
        else:
            final_confidence = total_confidence / total_weight
        
        # Apply user preferences
        final_confidence = self._apply_user_preferences(user_id, action_type, final_confidence)
        
        # Determine autonomy level
        autonomy_level = self._determine_autonomy_level(final_confidence)
        
        # Generate reasoning
        reasoning = self._generate_reasoning(matching_patterns, final_confidence, autonomy_level)
        
        # Determine if action should be executed
        should_execute = autonomy_level in [AutonomyLevel.SILENT, AutonomyLevel.AUTONOMOUS]
        requires_confirmation = autonomy_level == AutonomyLevel.CONFIRMATION
        requires_user_input = autonomy_level == AutonomyLevel.QUESTION
        
        return {
            "autonomy_level": autonomy_level,
            "confidence": final_confidence,
            "reasoning": reasoning,
            "should_execute": should_execute,
            "requires_confirmation": requires_confirmation,
            "requires_user_input": requires_user_input,
            "matching_patterns": [p.pattern_id for p in matching_patterns]
        }
    
    def _find_matching_patterns(
        self, 
        action_type: ActionType, 
        action_data: Dict[str, Any], 
        context: Dict[str, Any]
    ) -> List[ActionPattern]:
        """Find patterns that match the current action"""
        
        matching_patterns = []
        
        for pattern in self.action_patterns.values():
            if pattern.action_type == action_type:
                match_score = self._calculate_pattern_match(pattern, action_data, context)
                if match_score > 0.5:  # Threshold for pattern matching
                    matching_patterns.append(pattern)
        
        # Sort by confidence and success rate
        matching_patterns.sort(
            key=lambda p: (p.confidence, p.success_count / max(p.total_attempts, 1)), 
            reverse=True
        )
        
        return matching_patterns[:3]  # Top 3 patterns
    
    def _calculate_pattern_match(
        self, 
        pattern: ActionPattern, 
        action_data: Dict[str, Any], 
        context: Dict[str, Any]
    ) -> float:
        """Calculate how well a pattern matches the current situation"""
        
        total_conditions = len(pattern.conditions)
        if total_conditions == 0:
            return 0.0
        
        matched_conditions = 0
        
        for condition_key, condition_value in pattern.conditions.items():
            # Check in action_data first, then context
            actual_value = action_data.get(condition_key) or context.get(condition_key)
            
            if actual_value is not None:
                if isinstance(condition_value, bool):
                    if actual_value == condition_value:
                        matched_conditions += 1
                elif isinstance(condition_value, str):
                    if str(actual_value).lower() == condition_value.lower():
                        matched_conditions += 1
                elif isinstance(condition_value, list):
                    if actual_value in condition_value:
                        matched_conditions += 1
                elif isinstance(condition_value, dict):
                    # For complex matching (ranges, etc.)
                    if self._match_complex_condition(actual_value, condition_value):
                        matched_conditions += 1
        
        return matched_conditions / total_conditions
    
    def _match_complex_condition(self, actual_value: Any, condition: Dict[str, Any]) -> bool:
        """Match complex conditions like ranges, patterns, etc."""
        
        if "range" in condition:
            min_val, max_val = condition["range"]
            try:
                return min_val <= float(actual_value) <= max_val
            except (ValueError, TypeError):
                return False
        
        if "contains" in condition:
            return condition["contains"].lower() in str(actual_value).lower()
        
        if "regex" in condition:
            import re
            return bool(re.search(condition["regex"], str(actual_value)))
        
        return False
    
    def _calculate_pattern_weight(self, pattern: ActionPattern) -> float:
        """Calculate weight for a pattern based on its performance"""
        
        if pattern.total_attempts == 0:
            return 0.5  # Default weight for unused patterns
        
        success_rate = pattern.success_count / pattern.total_attempts
        feedback_weight = (pattern.user_feedback_score + 1) / 2  # Normalize -1,1 to 0,1
        
        # Recency weight (patterns used recently get higher weight)
        recency_weight = 1.0
        if pattern.last_used:
            days_since_use = (datetime.now() - pattern.last_used).days
            recency_weight = max(0.1, 1.0 - (days_since_use / 30))  # Decay over 30 days
        
        return success_rate * 0.4 + feedback_weight * 0.4 + recency_weight * 0.2
    
    def _apply_user_preferences(self, user_id: str, action_type: ActionType, confidence: float) -> float:
        """Apply user preferences to adjust confidence"""
        
        user_prefs = self.user_preferences.get(user_id, {})
        
        # Global autonomy preference
        autonomy_preference = user_prefs.get("autonomy_level", 0.5)  # 0-1 scale
        
        # Action-specific preferences
        action_prefs = user_prefs.get("action_preferences", {})
        action_pref = action_prefs.get(action_type.value, 0.5)
        
        # Combine preferences
        preference_multiplier = (autonomy_preference + action_pref) / 2
        
        # Apply conservative or aggressive adjustment
        if preference_multiplier < 0.3:  # Conservative user
            confidence *= 0.7
        elif preference_multiplier > 0.7:  # Aggressive user
            confidence *= 1.3
        
        return min(confidence, 1.0)
    
    def _determine_autonomy_level(self, confidence: float) -> AutonomyLevel:
        """Determine autonomy level based on confidence"""
        
        for level, (min_conf, max_conf) in self.confidence_thresholds.items():
            if min_conf <= confidence < max_conf:
                return level
        
        return AutonomyLevel.AUTONOMOUS  # For confidence >= 0.9
    
    def _generate_reasoning(
        self, 
        patterns: List[ActionPattern], 
        confidence: float, 
        autonomy_level: AutonomyLevel
    ) -> str:
        """Generate human-readable reasoning for the decision"""
        
        if not patterns:
            return "No learned patterns available for this action."
        
        primary_pattern = patterns[0]
        
        reasoning_parts = [
            f"Based on pattern '{primary_pattern.pattern_id}' with {primary_pattern.success_count} successful uses",
            f"Confidence: {confidence:.1%}",
            f"Action level: {autonomy_level.value}"
        ]
        
        if len(patterns) > 1:
            reasoning_parts.append(f"Considered {len(patterns)} similar patterns")
        
        if primary_pattern.user_feedback_score > 0.5:
            reasoning_parts.append("Previous user feedback was positive")
        elif primary_pattern.user_feedback_score < -0.5:
            reasoning_parts.append("Previous user feedback was negative")
        
        return ". ".join(reasoning_parts) + "."
    
    async def execute_action(
        self, 
        user_id: str, 
        action_type: ActionType, 
        action_data: Dict[str, Any],
        autonomy_level: AutonomyLevel,
        confidence: float
    ) -> Dict[str, Any]:
        """Execute an action based on autonomy level"""
        
        action_id = self._generate_action_id(user_id, action_type, action_data)
        
        if autonomy_level == AutonomyLevel.QUESTION:
            # Ask user for input
            return await self._request_user_input(user_id, action_id, action_type, action_data)
        
        elif autonomy_level == AutonomyLevel.CONFIRMATION:
            # Request confirmation
            return await self._request_confirmation(user_id, action_id, action_type, action_data, confidence)
        
        elif autonomy_level in [AutonomyLevel.SILENT, AutonomyLevel.AUTONOMOUS]:
            # Execute directly
            return await self._execute_direct_action(user_id, action_id, action_type, action_data, autonomy_level)
        
        else:
            raise ValueError(f"Unknown autonomy level: {autonomy_level}")
    
    async def _request_user_input(
        self, 
        user_id: str, 
        action_id: str, 
        action_type: ActionType, 
        action_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Request user input for low-confidence actions"""
        
        # Generate questions using LLM
        llm_pool = get_llm_pool()
        
        messages = [
            {
                "role": "system",
                "content": "You are an AI assistant asking for user guidance. Generate clear, specific questions."
            },
            {
                "role": "user",
                "content": f"I want to {action_type.value} but I'm not sure how to proceed. The context is: {json.dumps(action_data, indent=2)}. What questions should I ask the user?"
            }
        ]
        
        try:
            response = await llm_pool.generate_response(
                messages=messages,
                task_type=TaskType.QUICK_QUESTIONS,
                requirements={"max_tokens": 200}
            )
            
            questions = response["content"]
        except Exception as e:
            logger.error(f"Failed to generate questions: {e}")
            questions = f"How would you like me to proceed with {action_type.value}?"
        
        return {
            "status": "user_input_required",
            "action_id": action_id,
            "questions": questions,
            "action_type": action_type.value,
            "action_data": action_data
        }
    
    async def _request_confirmation(
        self, 
        user_id: str, 
        action_id: str, 
        action_type: ActionType, 
        action_data: Dict[str, Any],
        confidence: float
    ) -> Dict[str, Any]:
        """Request user confirmation for medium-confidence actions"""
        
        # Store pending action
        pending_action = PendingAction(
            action_id=action_id,
            user_id=user_id,
            action_type=action_type,
            action_data=action_data,
            confidence=confidence,
            autonomy_level=AutonomyLevel.CONFIRMATION,
            reasoning=f"Action confidence: {confidence:.1%}"
        )
        
        self.pending_actions[action_id] = pending_action
        
        # Generate confirmation message
        confirmation_message = self._generate_confirmation_message(action_type, action_data)
        
        return {
            "status": "confirmation_required",
            "action_id": action_id,
            "message": confirmation_message,
            "confidence": confidence,
            "expires_at": pending_action.expires_at.isoformat()
        }
    
    def _generate_confirmation_message(self, action_type: ActionType, action_data: Dict[str, Any]) -> str:
        """Generate a confirmation message for the user"""
        
        if action_type == ActionType.EMAIL_RESPONSE:
            recipient = action_data.get("recipient", "unknown")
            return f"Should I send a response to {recipient}?"
        
        elif action_type == ActionType.CALENDAR_UPDATE:
            event = action_data.get("event_title", "event")
            return f"Should I update the calendar event '{event}'?"
        
        elif action_type == ActionType.CONTEXT_SWITCH:
            context = action_data.get("target_context", "unknown context")
            return f"Should I switch to '{context}'?"
        
        else:
            return f"Should I proceed with {action_type.value}?"
    
    async def _execute_direct_action(
        self, 
        user_id: str, 
        action_id: str, 
        action_type: ActionType, 
        action_data: Dict[str, Any],
        autonomy_level: AutonomyLevel
    ) -> Dict[str, Any]:
        """Execute action directly without user intervention"""
        
        try:
            # Log the action
            log_level = "INFO" if autonomy_level == AutonomyLevel.SILENT else "DEBUG"
            logger.log(
                getattr(logging, log_level),
                f"Executing {action_type.value} autonomously for user {user_id}"
            )
            
            # Execute the actual action
            result = await self._perform_action(action_type, action_data, user_id)
            
            # Update pattern success
            await self._update_pattern_success(action_type, action_data, True)
            
            return {
                "status": "executed",
                "action_id": action_id,
                "result": result,
                "autonomy_level": autonomy_level.value,
                "executed_at": datetime.now().isoformat()
            }
        
        except Exception as e:
            logger.error(f"Failed to execute action {action_id}: {e}")
            
            # Update pattern failure
            await self._update_pattern_success(action_type, action_data, False)
            
            return {
                "status": "failed",
                "action_id": action_id,
                "error": str(e),
                "autonomy_level": autonomy_level.value
            }
    
    async def _perform_action(self, action_type: ActionType, action_data: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """Perform the actual action"""
        
        # TODO: Implement actual action execution
        # This would integrate with various services based on action_type
        
        if action_type == ActionType.EMAIL_RESPONSE:
            # Integrate with Gmail API
            return {"message": "Email response sent"}
        
        elif action_type == ActionType.CALENDAR_UPDATE:
            # Integrate with Calendar API
            return {"message": "Calendar updated"}
        
        elif action_type == ActionType.CONTEXT_SWITCH:
            # Integrate with context hierarchy
            from context_hierarchy import get_context_hierarchy_manager
            hierarchy_manager = get_context_hierarchy_manager()
            
            target_context = action_data.get("target_context_id")
            if target_context:
                result = await hierarchy_manager.switch_context(
                    user_id, target_context, trigger="autonomous"
                )
                return result
        
        # Default: simulate action
        return {"message": f"Simulated {action_type.value} execution", "data": action_data}
    
    async def _update_pattern_success(self, action_type: ActionType, action_data: Dict[str, Any], success: bool):
        """Update pattern statistics based on action outcome"""
        
        # Find patterns that would have matched this action
        matching_patterns = self._find_matching_patterns(action_type, action_data, {})
        
        for pattern in matching_patterns:
            pattern.total_attempts += 1
            pattern.last_used = datetime.now()
            
            if success:
                pattern.success_count += 1
                # Increase confidence slightly
                pattern.confidence = min(pattern.confidence + self.learning_rate * 0.1, 1.0)
            else:
                pattern.failure_count += 1
                # Decrease confidence slightly
                pattern.confidence = max(pattern.confidence - self.learning_rate * 0.2, 0.0)
    
    def _generate_action_id(self, user_id: str, action_type: ActionType, action_data: Dict[str, Any]) -> str:
        """Generate unique action ID"""
        
        data_str = json.dumps(action_data, sort_keys=True)
        hash_input = f"{user_id}_{action_type.value}_{data_str}_{datetime.now().timestamp()}"
        return hashlib.md5(hash_input.encode()).hexdigest()[:12]
    
    async def handle_user_feedback(self, action_id: str, feedback: Dict[str, Any]) -> Dict[str, Any]:
        """Handle user feedback on actions"""
        
        feedback_type = feedback.get("type")  # "positive", "negative", "correction"
        feedback_score = feedback.get("score", 0)  # -1 to 1
        
        # Find and update relevant patterns
        # TODO: Implement pattern learning from feedback
        
        return {"status": "feedback_processed", "action_id": action_id}
    
    def get_autonomy_stats(self, user_id: str) -> Dict[str, Any]:
        """Get autonomy system statistics"""
        
        user_patterns = [p for p in self.action_patterns.values()]
        
        total_patterns = len(user_patterns)
        total_attempts = sum(p.total_attempts for p in user_patterns)
        total_successes = sum(p.success_count for p in user_patterns)
        
        success_rate = total_successes / total_attempts if total_attempts > 0 else 0
        
        avg_confidence = sum(p.confidence for p in user_patterns) / total_patterns if total_patterns > 0 else 0
        
        return {
            "total_patterns": total_patterns,
            "total_attempts": total_attempts,
            "success_rate": success_rate,
            "average_confidence": avg_confidence,
            "pending_actions": len(self.pending_actions),
            "user_preferences": self.user_preferences.get(user_id, {})
        }

# Global autonomy system instance
autonomy_system = None

def initialize_autonomy_system():
    """Initialize the global autonomy system"""
    global autonomy_system
    autonomy_system = ConfidenceBasedAutonomy()
    logger.info("Confidence-Based Autonomy System initialized successfully")

def get_autonomy_system() -> ConfidenceBasedAutonomy:
    """Get the global autonomy system instance"""
    if not autonomy_system:
        initialize_autonomy_system()
    return autonomy_system
