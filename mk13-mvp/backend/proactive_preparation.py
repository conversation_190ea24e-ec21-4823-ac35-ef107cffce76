"""
Proactive Context Preparation System for MK13 MVP
Proactive data loading, meeting preparation, CRM updates, and template preparation
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
from database import get_managers
from google_workspace import get_google_services
from llm_pool import TaskType, get_llm_pool
from context_detection import get_context_detector, ContextTrigger

logger = logging.getLogger(__name__)

class PreparationType(Enum):
    """Types of proactive preparation"""
    MEETING_PREP = "meeting_prep"
    EMAIL_CONTEXT = "email_context"
    PROJECT_CONTEXT = "project_context"
    DAILY_BRIEFING = "daily_briefing"
    DOCUMENT_TEMPLATES = "document_templates"
    CRM_UPDATE = "crm_update"
    RESEARCH_BRIEF = "research_brief"

@dataclass
class PreparationTask:
    """Represents a proactive preparation task"""
    task_id: str
    preparation_type: PreparationType
    context_name: str
    user_id: str
    trigger_data: Dict[str, Any]
    priority: int  # 1-10, 10 being highest
    estimated_duration: int  # minutes
    deadline: datetime
    status: str = "pending"  # pending, in_progress, completed, failed
    result: Optional[Dict[str, Any]] = None
    created_at: datetime = field(default_factory=datetime.now)

class ProactivePreparationEngine:
    """Engine for proactive context preparation"""
    
    def __init__(self):
        self.preparation_queue = []
        self.active_tasks = {}
        self.preparation_templates = {}
        self.user_preferences = {}
        
        self._initialize_templates()
    
    def _initialize_templates(self):
        """Initialize preparation templates"""
        
        self.preparation_templates = {
            PreparationType.MEETING_PREP: {
                "data_sources": ["calendar", "email", "crm", "documents"],
                "preparation_steps": [
                    "gather_participant_info",
                    "review_previous_meetings",
                    "prepare_agenda_template",
                    "collect_relevant_documents",
                    "research_discussion_topics"
                ],
                "output_format": "meeting_brief"
            },
            
            PreparationType.EMAIL_CONTEXT: {
                "data_sources": ["email_history", "crm", "calendar"],
                "preparation_steps": [
                    "analyze_sender_relationship",
                    "review_communication_history",
                    "identify_action_items",
                    "prepare_response_templates"
                ],
                "output_format": "email_context_brief"
            },
            
            PreparationType.DAILY_BRIEFING: {
                "data_sources": ["calendar", "email", "tasks", "news"],
                "preparation_steps": [
                    "summarize_todays_schedule",
                    "highlight_priority_emails",
                    "review_pending_tasks",
                    "gather_relevant_updates"
                ],
                "output_format": "daily_brief"
            },
            
            PreparationType.PROJECT_CONTEXT: {
                "data_sources": ["documents", "email", "calendar", "tasks"],
                "preparation_steps": [
                    "gather_project_documents",
                    "review_recent_communications",
                    "analyze_project_status",
                    "identify_blockers_and_risks"
                ],
                "output_format": "project_brief"
            }
        }
    
    async def schedule_preparation(
        self, 
        preparation_type: PreparationType,
        context_name: str,
        user_id: str,
        trigger_data: Dict[str, Any],
        deadline: datetime,
        priority: int = 5
    ) -> str:
        """Schedule a proactive preparation task"""
        
        task_id = f"{preparation_type.value}_{user_id}_{int(datetime.now().timestamp())}"
        
        # Estimate duration based on preparation type
        duration_estimates = {
            PreparationType.MEETING_PREP: 10,
            PreparationType.EMAIL_CONTEXT: 3,
            PreparationType.DAILY_BRIEFING: 15,
            PreparationType.PROJECT_CONTEXT: 20,
            PreparationType.DOCUMENT_TEMPLATES: 5,
            PreparationType.CRM_UPDATE: 8,
            PreparationType.RESEARCH_BRIEF: 12
        }
        
        estimated_duration = duration_estimates.get(preparation_type, 10)
        
        task = PreparationTask(
            task_id=task_id,
            preparation_type=preparation_type,
            context_name=context_name,
            user_id=user_id,
            trigger_data=trigger_data,
            priority=priority,
            estimated_duration=estimated_duration,
            deadline=deadline
        )
        
        # Add to queue (sorted by priority and deadline)
        self.preparation_queue.append(task)
        self.preparation_queue.sort(key=lambda t: (-t.priority, t.deadline))
        
        logger.info(f"Scheduled preparation task: {task_id}")
        
        # Start processing if not already running
        asyncio.create_task(self._process_preparation_queue())
        
        return task_id
    
    async def _process_preparation_queue(self):
        """Process the preparation queue"""
        
        while self.preparation_queue:
            task = self.preparation_queue.pop(0)
            
            # Check if deadline has passed
            if datetime.now() > task.deadline:
                logger.warning(f"Preparation task {task.task_id} missed deadline")
                task.status = "failed"
                continue
            
            # Check if already processing similar task
            if self._is_duplicate_task(task):
                logger.info(f"Skipping duplicate task: {task.task_id}")
                continue
            
            # Process the task
            self.active_tasks[task.task_id] = task
            task.status = "in_progress"
            
            try:
                result = await self._execute_preparation(task)
                task.result = result
                task.status = "completed"
                
                # Store result in context
                await self._store_preparation_result(task)
                
                logger.info(f"Completed preparation task: {task.task_id}")
                
            except Exception as e:
                logger.error(f"Failed preparation task {task.task_id}: {e}")
                task.status = "failed"
            
            finally:
                if task.task_id in self.active_tasks:
                    del self.active_tasks[task.task_id]
    
    def _is_duplicate_task(self, task: PreparationTask) -> bool:
        """Check if a similar task is already being processed"""
        
        for active_task in self.active_tasks.values():
            if (active_task.preparation_type == task.preparation_type and
                active_task.user_id == task.user_id and
                active_task.context_name == task.context_name):
                return True
        
        return False
    
    async def _execute_preparation(self, task: PreparationTask) -> Dict[str, Any]:
        """Execute a preparation task"""
        
        template = self.preparation_templates.get(task.preparation_type)
        if not template:
            raise ValueError(f"No template for preparation type: {task.preparation_type}")
        
        result = {
            "task_id": task.task_id,
            "preparation_type": task.preparation_type.value,
            "context_name": task.context_name,
            "prepared_data": {},
            "suggestions": [],
            "templates": [],
            "timestamp": datetime.now().isoformat()
        }
        
        # Execute preparation steps
        for step in template["preparation_steps"]:
            step_result = await self._execute_preparation_step(step, task)
            result["prepared_data"][step] = step_result
        
        # Generate AI-powered insights
        insights = await self._generate_preparation_insights(task, result["prepared_data"])
        result["insights"] = insights
        
        return result
    
    async def _execute_preparation_step(self, step: str, task: PreparationTask) -> Dict[str, Any]:
        """Execute a specific preparation step"""
        
        try:
            if step == "gather_participant_info":
                return await self._gather_participant_info(task)
            
            elif step == "review_previous_meetings":
                return await self._review_previous_meetings(task)
            
            elif step == "prepare_agenda_template":
                return await self._prepare_agenda_template(task)
            
            elif step == "collect_relevant_documents":
                return await self._collect_relevant_documents(task)
            
            elif step == "research_discussion_topics":
                return await self._research_discussion_topics(task)
            
            elif step == "analyze_sender_relationship":
                return await self._analyze_sender_relationship(task)
            
            elif step == "review_communication_history":
                return await self._review_communication_history(task)
            
            elif step == "summarize_todays_schedule":
                return await self._summarize_todays_schedule(task)
            
            elif step == "highlight_priority_emails":
                return await self._highlight_priority_emails(task)
            
            else:
                logger.warning(f"Unknown preparation step: {step}")
                return {"status": "skipped", "reason": "unknown_step"}
        
        except Exception as e:
            logger.error(f"Error in preparation step {step}: {e}")
            return {"status": "error", "error": str(e)}
    
    async def _gather_participant_info(self, task: PreparationTask) -> Dict[str, Any]:
        """Gather information about meeting participants"""
        
        participants = task.trigger_data.get("participants", [])
        participant_info = []
        
        for participant in participants:
            # TODO: Integrate with CRM or directory service
            info = {
                "email": participant.get("email", ""),
                "name": participant.get("name", ""),
                "role": "Unknown",  # Would come from CRM
                "recent_interactions": [],  # Would come from email/calendar history
                "notes": ""
            }
            participant_info.append(info)
        
        return {
            "participants": participant_info,
            "total_participants": len(participant_info)
        }
    
    async def _review_previous_meetings(self, task: PreparationTask) -> Dict[str, Any]:
        """Review previous meetings with similar participants or topics"""
        
        # TODO: Implement meeting history analysis
        return {
            "previous_meetings": [],
            "common_topics": [],
            "action_items_from_previous": []
        }
    
    async def _prepare_agenda_template(self, task: PreparationTask) -> Dict[str, Any]:
        """Prepare an agenda template for the meeting"""
        
        meeting_title = task.trigger_data.get("title", "Meeting")
        
        # Generate agenda using LLM
        llm_pool = get_llm_pool()
        
        messages = [
            {
                "role": "system",
                "content": "You are an expert meeting facilitator. Create a professional meeting agenda."
            },
            {
                "role": "user",
                "content": f"Create a meeting agenda for: {meeting_title}. Include time allocations and key discussion points."
            }
        ]
        
        try:
            response = await llm_pool.generate_response(
                messages=messages,
                task_type=TaskType.DOCUMENT_ANALYSIS,
                requirements={"max_tokens": 500}
            )
            
            agenda = response["content"]
        except Exception as e:
            logger.error(f"Failed to generate agenda: {e}")
            agenda = f"1. Welcome and introductions\n2. {meeting_title} - Discussion\n3. Action items\n4. Next steps"
        
        return {
            "agenda": agenda,
            "estimated_duration": task.trigger_data.get("duration", "60 minutes")
        }
    
    async def _collect_relevant_documents(self, task: PreparationTask) -> Dict[str, Any]:
        """Collect documents relevant to the meeting/context"""
        
        # TODO: Implement document search and collection
        return {
            "documents": [],
            "search_keywords": [],
            "document_summaries": []
        }
    
    async def _research_discussion_topics(self, task: PreparationTask) -> Dict[str, Any]:
        """Research topics that might be discussed"""
        
        meeting_title = task.trigger_data.get("title", "")
        
        # Extract keywords from meeting title
        keywords = meeting_title.lower().split()
        
        # TODO: Implement web research or knowledge base search
        return {
            "topics": keywords,
            "background_info": {},
            "potential_questions": []
        }
    
    async def _analyze_sender_relationship(self, task: PreparationTask) -> Dict[str, Any]:
        """Analyze relationship with email sender"""
        
        sender = task.trigger_data.get("sender", "")
        
        # TODO: Implement CRM integration for relationship analysis
        return {
            "sender": sender,
            "relationship_type": "unknown",
            "communication_frequency": "unknown",
            "last_interaction": None
        }
    
    async def _review_communication_history(self, task: PreparationTask) -> Dict[str, Any]:
        """Review communication history with sender"""
        
        # TODO: Implement email history analysis
        return {
            "recent_emails": [],
            "communication_patterns": {},
            "pending_responses": []
        }
    
    async def _summarize_todays_schedule(self, task: PreparationTask) -> Dict[str, Any]:
        """Summarize today's schedule"""
        
        try:
            google_services = get_google_services()
            events = await google_services["calendar"].get_upcoming_events(task.user_id, max_results=20)
            
            # Filter for today's events
            today = datetime.now().date()
            todays_events = [
                event for event in events 
                if datetime.fromisoformat(event["start"].replace("Z", "+00:00")).date() == today
            ]
            
            return {
                "total_events": len(todays_events),
                "events": todays_events,
                "busy_periods": [],  # TODO: Calculate busy periods
                "free_slots": []     # TODO: Calculate free slots
            }
        
        except Exception as e:
            logger.error(f"Error summarizing schedule: {e}")
            return {"error": str(e)}
    
    async def _highlight_priority_emails(self, task: PreparationTask) -> Dict[str, Any]:
        """Highlight priority emails"""
        
        try:
            google_services = get_google_services()
            emails = await google_services["gmail"].get_recent_emails(task.user_id, max_results=20)
            
            # TODO: Implement priority scoring
            priority_emails = emails[:5]  # Simple: take first 5
            
            return {
                "priority_emails": priority_emails,
                "total_unread": len(emails),
                "urgent_count": 0  # TODO: Count urgent emails
            }
        
        except Exception as e:
            logger.error(f"Error highlighting emails: {e}")
            return {"error": str(e)}
    
    async def _generate_preparation_insights(self, task: PreparationTask, prepared_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate AI-powered insights from prepared data"""
        
        try:
            llm_pool = get_llm_pool()
            
            # Create summary of prepared data
            data_summary = json.dumps(prepared_data, indent=2, default=str)
            
            messages = [
                {
                    "role": "system",
                    "content": "You are an AI assistant that provides actionable insights based on prepared context data."
                },
                {
                    "role": "user",
                    "content": f"Analyze this prepared data and provide 3-5 actionable insights:\n\n{data_summary}"
                }
            ]
            
            response = await llm_pool.generate_response(
                messages=messages,
                task_type=TaskType.COMPLEX_REASONING,
                requirements={"max_tokens": 300}
            )
            
            return {
                "insights": response["content"],
                "confidence": 0.8,
                "generated_at": datetime.now().isoformat()
            }
        
        except Exception as e:
            logger.error(f"Error generating insights: {e}")
            return {"error": str(e)}
    
    async def _store_preparation_result(self, task: PreparationTask):
        """Store preparation result in user context"""
        
        try:
            managers = get_managers()
            
            # Update or create context with preparation result
            contexts = await managers["contexts"].get_user_contexts(task.user_id)
            target_context = next(
                (ctx for ctx in contexts if ctx["name"] == task.context_name), 
                None
            )
            
            if target_context:
                # Update existing context
                updated_state = target_context["state"].copy()
                updated_state["preparation"] = task.result
                updated_state["prepared_at"] = datetime.now().isoformat()
                
                await managers["contexts"].update_context(
                    target_context["id"], 
                    {"state": updated_state}
                )
            else:
                # Create new context with preparation
                await managers["contexts"].create_context(
                    user_id=task.user_id,
                    name=task.context_name,
                    state={
                        "preparation": task.result,
                        "prepared_at": datetime.now().isoformat(),
                        "preparation_type": task.preparation_type.value
                    },
                    metadata={
                        "auto_prepared": True,
                        "preparation_task_id": task.task_id
                    }
                )
        
        except Exception as e:
            logger.error(f"Error storing preparation result: {e}")
    
    def get_preparation_status(self, user_id: str) -> Dict[str, Any]:
        """Get preparation status for a user"""
        
        user_tasks = [
            task for task in self.preparation_queue + list(self.active_tasks.values())
            if task.user_id == user_id
        ]
        
        return {
            "total_tasks": len(user_tasks),
            "pending": len([t for t in user_tasks if t.status == "pending"]),
            "in_progress": len([t for t in user_tasks if t.status == "in_progress"]),
            "completed": len([t for t in user_tasks if t.status == "completed"]),
            "failed": len([t for t in user_tasks if t.status == "failed"]),
            "active_tasks": [
                {
                    "task_id": task.task_id,
                    "type": task.preparation_type.value,
                    "context": task.context_name,
                    "status": task.status,
                    "deadline": task.deadline.isoformat()
                }
                for task in user_tasks
            ]
        }

# Global preparation engine instance
preparation_engine = None

def initialize_preparation_engine():
    """Initialize the global preparation engine"""
    global preparation_engine
    preparation_engine = ProactivePreparationEngine()
    logger.info("Proactive Preparation Engine initialized successfully")

def get_preparation_engine() -> ProactivePreparationEngine:
    """Get the global preparation engine instance"""
    if not preparation_engine:
        initialize_preparation_engine()
    return preparation_engine
