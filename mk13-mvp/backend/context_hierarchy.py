"""
Context Hierarchy & Switching System for MK13 MVP
Manages active/background/suspended contexts with seamless switching
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
from database import get_managers
from context_detection import get_context_detector
from proactive_preparation import get_preparation_engine, PreparationType

logger = logging.getLogger(__name__)

class ContextState(Enum):
    """Context states in the hierarchy"""
    ACTIVE = "active"           # Currently focused context
    BACKGROUND = "background"   # Running but not focused
    SUSPENDED = "suspended"     # Paused, can be resumed
    ARCHIVED = "archived"       # Completed or no longer relevant

class ContextPriority(Enum):
    """Context priority levels"""
    CRITICAL = "critical"       # Urgent, interrupts everything
    HIGH = "high"              # Important, interrupts low priority
    MEDIUM = "medium"          # Normal priority
    LOW = "low"                # Background, easily interrupted

@dataclass
class ContextMetrics:
    """Metrics for context usage and performance"""
    total_time_spent: float = 0.0  # Total time in minutes
    switch_count: int = 0
    interruption_count: int = 0
    completion_rate: float = 0.0
    last_accessed: Optional[datetime] = None
    productivity_score: float = 0.0  # 0-1 based on task completion

@dataclass
class ContextHierarchyNode:
    """Node in the context hierarchy"""
    context_id: str
    context_name: str
    state: ContextState
    priority: ContextPriority
    user_id: str
    parent_context_id: Optional[str] = None
    child_context_ids: List[str] = field(default_factory=list)
    context_data: Dict[str, Any] = field(default_factory=dict)
    metrics: ContextMetrics = field(default_factory=ContextMetrics)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    # Context switching metadata
    switch_triggers: List[str] = field(default_factory=list)
    auto_resume_conditions: Dict[str, Any] = field(default_factory=dict)
    dependencies: List[str] = field(default_factory=list)

class ContextHierarchyManager:
    """Manages context hierarchy and switching"""
    
    def __init__(self):
        self.context_hierarchy = {}  # user_id -> context tree
        self.active_contexts = {}    # user_id -> active context_id
        self.context_history = {}    # user_id -> list of context switches
        self.switching_rules = {}
        self.auto_switch_patterns = {}
        
        self._initialize_switching_rules()
    
    def _initialize_switching_rules(self):
        """Initialize context switching rules"""
        
        self.switching_rules = {
            # Critical contexts can interrupt anything
            "critical_interrupt": {
                "from_priority": [ContextPriority.LOW, ContextPriority.MEDIUM, ContextPriority.HIGH],
                "to_priority": ContextPriority.CRITICAL,
                "action": "immediate_switch"
            },
            
            # High priority can interrupt low/medium
            "high_interrupt": {
                "from_priority": [ContextPriority.LOW, ContextPriority.MEDIUM],
                "to_priority": ContextPriority.HIGH,
                "action": "confirm_switch"
            },
            
            # Medium can interrupt low
            "medium_interrupt": {
                "from_priority": [ContextPriority.LOW],
                "to_priority": ContextPriority.MEDIUM,
                "action": "suggest_switch"
            },
            
            # Same priority requires confirmation
            "same_priority": {
                "from_priority": "same",
                "to_priority": "same",
                "action": "confirm_switch"
            }
        }
    
    async def create_context_hierarchy(
        self, 
        user_id: str, 
        context_name: str, 
        priority: ContextPriority = ContextPriority.MEDIUM,
        parent_context_id: Optional[str] = None,
        context_data: Dict[str, Any] = None
    ) -> str:
        """Create a new context in the hierarchy"""
        
        context_data = context_data or {}
        
        # Generate unique context ID
        context_id = f"ctx_{user_id}_{int(datetime.now().timestamp())}"
        
        # Create context node
        context_node = ContextHierarchyNode(
            context_id=context_id,
            context_name=context_name,
            state=ContextState.SUSPENDED,  # Start as suspended
            priority=priority,
            user_id=user_id,
            parent_context_id=parent_context_id,
            context_data=context_data
        )
        
        # Initialize user hierarchy if needed
        if user_id not in self.context_hierarchy:
            self.context_hierarchy[user_id] = {}
            self.context_history[user_id] = []
        
        # Add to hierarchy
        self.context_hierarchy[user_id][context_id] = context_node
        
        # Update parent-child relationships
        if parent_context_id and parent_context_id in self.context_hierarchy[user_id]:
            parent_node = self.context_hierarchy[user_id][parent_context_id]
            parent_node.child_context_ids.append(context_id)
        
        # Store in database
        await self._persist_context_hierarchy(user_id, context_id)
        
        logger.info(f"Created context hierarchy node: {context_id} for user {user_id}")
        
        return context_id
    
    async def switch_context(
        self, 
        user_id: str, 
        target_context_id: str,
        trigger: str = "manual",
        force: bool = False
    ) -> Dict[str, Any]:
        """Switch to a different context"""
        
        if user_id not in self.context_hierarchy:
            raise ValueError(f"No context hierarchy for user {user_id}")
        
        if target_context_id not in self.context_hierarchy[user_id]:
            raise ValueError(f"Context {target_context_id} not found")
        
        current_context_id = self.active_contexts.get(user_id)
        target_context = self.context_hierarchy[user_id][target_context_id]
        
        # Check switching rules if not forced
        if not force and current_context_id:
            current_context = self.context_hierarchy[user_id][current_context_id]
            switch_decision = await self._evaluate_context_switch(
                current_context, target_context, trigger
            )
            
            if switch_decision["action"] == "deny":
                return {
                    "success": False,
                    "reason": switch_decision["reason"],
                    "suggestion": switch_decision.get("suggestion")
                }
            elif switch_decision["action"] == "confirm":
                return {
                    "success": False,
                    "requires_confirmation": True,
                    "message": switch_decision["message"],
                    "switch_token": f"switch_{user_id}_{target_context_id}_{int(datetime.now().timestamp())}"
                }
        
        # Perform the switch
        switch_result = await self._execute_context_switch(
            user_id, current_context_id, target_context_id, trigger
        )
        
        return switch_result
    
    async def _evaluate_context_switch(
        self, 
        current_context: ContextHierarchyNode, 
        target_context: ContextHierarchyNode,
        trigger: str
    ) -> Dict[str, Any]:
        """Evaluate whether a context switch should be allowed"""
        
        # Check priority-based rules
        for rule_name, rule in self.switching_rules.items():
            if rule["to_priority"] == target_context.priority:
                if (rule["from_priority"] == "same" and 
                    current_context.priority == target_context.priority):
                    return {
                        "action": rule["action"],
                        "message": f"Switch from {current_context.context_name} to {target_context.context_name}?",
                        "rule": rule_name
                    }
                elif current_context.priority in rule.get("from_priority", []):
                    if rule["action"] == "immediate_switch":
                        return {"action": "allow", "rule": rule_name}
                    else:
                        return {
                            "action": rule["action"],
                            "message": f"Switch from {current_context.context_name} to {target_context.context_name}?",
                            "rule": rule_name
                        }
        
        # Check dependencies
        if target_context.dependencies:
            for dep_id in target_context.dependencies:
                if dep_id in self.context_hierarchy[current_context.user_id]:
                    dep_context = self.context_hierarchy[current_context.user_id][dep_id]
                    if dep_context.state not in [ContextState.ACTIVE, ContextState.BACKGROUND]:
                        return {
                            "action": "deny",
                            "reason": f"Dependency context '{dep_context.context_name}' is not active",
                            "suggestion": f"Activate '{dep_context.context_name}' first"
                        }
        
        # Default: allow switch
        return {"action": "allow"}
    
    async def _execute_context_switch(
        self, 
        user_id: str, 
        from_context_id: Optional[str], 
        to_context_id: str,
        trigger: str
    ) -> Dict[str, Any]:
        """Execute the actual context switch"""
        
        timestamp = datetime.now()
        
        # Update current context to background/suspended
        if from_context_id:
            from_context = self.context_hierarchy[user_id][from_context_id]
            
            # Save current state
            await self._save_context_state(from_context)
            
            # Determine new state based on priority and user preference
            if from_context.priority in [ContextPriority.HIGH, ContextPriority.CRITICAL]:
                from_context.state = ContextState.BACKGROUND
            else:
                from_context.state = ContextState.SUSPENDED
            
            from_context.updated_at = timestamp
            from_context.metrics.switch_count += 1
            
            # Calculate time spent
            if from_context.metrics.last_accessed:
                time_spent = (timestamp - from_context.metrics.last_accessed).total_seconds() / 60
                from_context.metrics.total_time_spent += time_spent
        
        # Activate target context
        to_context = self.context_hierarchy[user_id][to_context_id]
        to_context.state = ContextState.ACTIVE
        to_context.updated_at = timestamp
        to_context.metrics.last_accessed = timestamp
        to_context.switch_triggers.append(trigger)
        
        # Update active context
        self.active_contexts[user_id] = to_context_id
        
        # Record switch in history
        switch_record = {
            "timestamp": timestamp.isoformat(),
            "from_context": from_context_id,
            "to_context": to_context_id,
            "trigger": trigger,
            "switch_duration": 0  # Time taken to switch
        }
        self.context_history[user_id].append(switch_record)
        
        # Keep only last 100 switches
        if len(self.context_history[user_id]) > 100:
            self.context_history[user_id] = self.context_history[user_id][-100:]
        
        # Load context state
        await self._load_context_state(to_context)
        
        # Trigger proactive preparation if needed
        await self._trigger_context_preparation(to_context)
        
        # Persist changes
        await self._persist_context_hierarchy(user_id, to_context_id)
        if from_context_id:
            await self._persist_context_hierarchy(user_id, from_context_id)
        
        logger.info(f"Context switch completed: {from_context_id} -> {to_context_id} for user {user_id}")
        
        return {
            "success": True,
            "from_context": from_context_id,
            "to_context": to_context_id,
            "context_name": to_context.context_name,
            "trigger": trigger,
            "timestamp": timestamp.isoformat(),
            "preparation_triggered": True
        }
    
    async def _save_context_state(self, context: ContextHierarchyNode):
        """Save current context state before switching"""
        
        try:
            managers = get_managers()
            
            # Get current context from database
            contexts = await managers["contexts"].get_user_contexts(context.user_id)
            db_context = next(
                (ctx for ctx in contexts if ctx.get("hierarchy_id") == context.context_id), 
                None
            )
            
            if db_context:
                # Update with current state
                updated_state = db_context["state"].copy()
                updated_state.update({
                    "hierarchy_state": context.state.value,
                    "last_active": context.metrics.last_accessed.isoformat() if context.metrics.last_accessed else None,
                    "switch_count": context.metrics.switch_count,
                    "total_time_spent": context.metrics.total_time_spent
                })
                
                await managers["contexts"].update_context(
                    db_context["id"], 
                    {"state": updated_state}
                )
        
        except Exception as e:
            logger.error(f"Error saving context state: {e}")
    
    async def _load_context_state(self, context: ContextHierarchyNode):
        """Load context state when switching to it"""
        
        try:
            managers = get_managers()
            
            # Get context from database
            contexts = await managers["contexts"].get_user_contexts(context.user_id)
            db_context = next(
                (ctx for ctx in contexts if ctx.get("hierarchy_id") == context.context_id), 
                None
            )
            
            if db_context:
                # Load state into hierarchy node
                state = db_context["state"]
                context.context_data.update(state)
                
                # Restore metrics if available
                if "switch_count" in state:
                    context.metrics.switch_count = state["switch_count"]
                if "total_time_spent" in state:
                    context.metrics.total_time_spent = state["total_time_spent"]
        
        except Exception as e:
            logger.error(f"Error loading context state: {e}")
    
    async def _trigger_context_preparation(self, context: ContextHierarchyNode):
        """Trigger proactive preparation for the new context"""
        
        try:
            preparation_engine = get_preparation_engine()
            
            # Determine preparation type based on context
            prep_type = self._determine_preparation_type(context)
            
            if prep_type:
                # Schedule preparation
                deadline = datetime.now() + timedelta(minutes=5)  # 5 minutes to prepare
                
                await preparation_engine.schedule_preparation(
                    preparation_type=prep_type,
                    context_name=context.context_name,
                    user_id=context.user_id,
                    trigger_data=context.context_data,
                    deadline=deadline,
                    priority=8  # High priority for context switches
                )
        
        except Exception as e:
            logger.error(f"Error triggering context preparation: {e}")
    
    def _determine_preparation_type(self, context: ContextHierarchyNode) -> Optional[PreparationType]:
        """Determine what type of preparation is needed for a context"""
        
        context_name_lower = context.context_name.lower()
        
        if any(keyword in context_name_lower for keyword in ["meeting", "call", "conference"]):
            return PreparationType.MEETING_PREP
        elif any(keyword in context_name_lower for keyword in ["email", "message", "communication"]):
            return PreparationType.EMAIL_CONTEXT
        elif any(keyword in context_name_lower for keyword in ["project", "work", "task"]):
            return PreparationType.PROJECT_CONTEXT
        elif any(keyword in context_name_lower for keyword in ["daily", "planning", "review"]):
            return PreparationType.DAILY_BRIEFING
        
        return None
    
    async def _persist_context_hierarchy(self, user_id: str, context_id: str):
        """Persist context hierarchy to database"""
        
        try:
            managers = get_managers()
            context_node = self.context_hierarchy[user_id][context_id]
            
            # Check if context exists in database
            contexts = await managers["contexts"].get_user_contexts(user_id)
            db_context = next(
                (ctx for ctx in contexts if ctx.get("hierarchy_id") == context_id), 
                None
            )
            
            hierarchy_data = {
                "hierarchy_id": context_id,
                "hierarchy_state": context_node.state.value,
                "priority": context_node.priority.value,
                "parent_context_id": context_node.parent_context_id,
                "child_context_ids": context_node.child_context_ids,
                "metrics": {
                    "total_time_spent": context_node.metrics.total_time_spent,
                    "switch_count": context_node.metrics.switch_count,
                    "interruption_count": context_node.metrics.interruption_count,
                    "completion_rate": context_node.metrics.completion_rate,
                    "productivity_score": context_node.metrics.productivity_score
                },
                "switch_triggers": context_node.switch_triggers,
                "auto_resume_conditions": context_node.auto_resume_conditions,
                "dependencies": context_node.dependencies
            }
            
            if db_context:
                # Update existing context
                updated_state = db_context["state"].copy()
                updated_state.update(hierarchy_data)
                
                await managers["contexts"].update_context(
                    db_context["id"], 
                    {"state": updated_state}
                )
            else:
                # Create new context
                await managers["contexts"].create_context(
                    user_id=user_id,
                    name=context_node.context_name,
                    state=hierarchy_data,
                    metadata={
                        "hierarchy_managed": True,
                        "created_by": "context_hierarchy",
                        "priority": context_node.priority.value
                    }
                )
        
        except Exception as e:
            logger.error(f"Error persisting context hierarchy: {e}")
    
    def get_context_hierarchy(self, user_id: str) -> Dict[str, Any]:
        """Get the complete context hierarchy for a user"""
        
        if user_id not in self.context_hierarchy:
            return {"contexts": {}, "active_context": None, "hierarchy": []}
        
        contexts = {}
        for context_id, context_node in self.context_hierarchy[user_id].items():
            contexts[context_id] = {
                "id": context_id,
                "name": context_node.context_name,
                "state": context_node.state.value,
                "priority": context_node.priority.value,
                "parent_id": context_node.parent_context_id,
                "child_ids": context_node.child_context_ids,
                "metrics": {
                    "total_time_spent": context_node.metrics.total_time_spent,
                    "switch_count": context_node.metrics.switch_count,
                    "last_accessed": context_node.metrics.last_accessed.isoformat() if context_node.metrics.last_accessed else None
                },
                "created_at": context_node.created_at.isoformat(),
                "updated_at": context_node.updated_at.isoformat()
            }
        
        # Build hierarchy tree
        hierarchy = self._build_hierarchy_tree(user_id)
        
        return {
            "contexts": contexts,
            "active_context": self.active_contexts.get(user_id),
            "hierarchy": hierarchy,
            "switch_history": self.context_history.get(user_id, [])[-10:]  # Last 10 switches
        }
    
    def _build_hierarchy_tree(self, user_id: str) -> List[Dict[str, Any]]:
        """Build hierarchical tree structure"""
        
        if user_id not in self.context_hierarchy:
            return []
        
        # Find root contexts (no parent)
        root_contexts = [
            context for context in self.context_hierarchy[user_id].values()
            if context.parent_context_id is None
        ]
        
        def build_tree_node(context_node: ContextHierarchyNode) -> Dict[str, Any]:
            node = {
                "id": context_node.context_id,
                "name": context_node.context_name,
                "state": context_node.state.value,
                "priority": context_node.priority.value,
                "children": []
            }
            
            # Add children recursively
            for child_id in context_node.child_context_ids:
                if child_id in self.context_hierarchy[user_id]:
                    child_context = self.context_hierarchy[user_id][child_id]
                    node["children"].append(build_tree_node(child_context))
            
            return node
        
        return [build_tree_node(context) for context in root_contexts]
    
    async def auto_switch_context(self, user_id: str, trigger_data: Dict[str, Any]) -> Optional[str]:
        """Automatically switch context based on patterns and triggers"""
        
        # TODO: Implement intelligent auto-switching based on:
        # - Time patterns
        # - Application focus
        # - Calendar events
        # - Email triggers
        # - User behavior patterns
        
        return None
    
    def get_context_metrics(self, user_id: str) -> Dict[str, Any]:
        """Get context usage metrics"""
        
        if user_id not in self.context_hierarchy:
            return {}
        
        total_contexts = len(self.context_hierarchy[user_id])
        active_contexts = len([
            ctx for ctx in self.context_hierarchy[user_id].values()
            if ctx.state == ContextState.ACTIVE
        ])
        background_contexts = len([
            ctx for ctx in self.context_hierarchy[user_id].values()
            if ctx.state == ContextState.BACKGROUND
        ])
        
        total_switches = sum(
            ctx.metrics.switch_count 
            for ctx in self.context_hierarchy[user_id].values()
        )
        
        total_time = sum(
            ctx.metrics.total_time_spent 
            for ctx in self.context_hierarchy[user_id].values()
        )
        
        return {
            "total_contexts": total_contexts,
            "active_contexts": active_contexts,
            "background_contexts": background_contexts,
            "total_switches": total_switches,
            "total_time_spent": total_time,
            "average_time_per_context": total_time / total_contexts if total_contexts > 0 else 0,
            "switches_per_context": total_switches / total_contexts if total_contexts > 0 else 0
        }

# Global context hierarchy manager instance
context_hierarchy_manager = None

def initialize_context_hierarchy():
    """Initialize the global context hierarchy manager"""
    global context_hierarchy_manager
    context_hierarchy_manager = ContextHierarchyManager()
    logger.info("Context Hierarchy Manager initialized successfully")

def get_context_hierarchy_manager() -> ContextHierarchyManager:
    """Get the global context hierarchy manager instance"""
    if not context_hierarchy_manager:
        initialize_context_hierarchy()
    return context_hierarchy_manager
