"""
Multi-Provider LLM Pool for MK13 MVP
Intelligent routing, cost optimization, and fallback management
"""

import os
import time
import asyncio
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json
from datetime import datetime, timedelta
import openai
import anthropic
import google.generativeai as genai
from groq import Groq

logger = logging.getLogger(__name__)

class TaskType(Enum):
    """Task types for intelligent routing"""
    DOCUMENT_ANALYSIS = "document_analysis"
    CODE_GENERATION = "code_generation"
    QUICK_QUESTIONS = "quick_questions"
    IMAGE_ANALYSIS = "image_analysis"
    BACKGROUND_TASKS = "background_tasks"
    COMPLEX_REASONING = "complex_reasoning"
    REAL_TIME_CHAT = "real_time_chat"
    CREATIVE_WRITING = "creative_writing"
    DATA_EXTRACTION = "data_extraction"
    SUMMARIZATION = "summarization"

class OperationMode(Enum):
    """Operation modes for cost/performance balance"""
    ECONOMY = "economy"
    BALANCED = "balanced"
    PERFORMANCE = "performance"
    CUSTOM = "custom"

@dataclass
class ModelConfig:
    """Configuration for a specific model"""
    provider: str
    model_name: str
    cost_per_1k_tokens: float
    max_tokens: int
    supports_vision: bool = False
    supports_function_calling: bool = False
    speed_rating: int = 1  # 1-10, 10 being fastest
    quality_rating: int = 1  # 1-10, 10 being highest quality
    rate_limit_rpm: int = 60  # requests per minute
    rate_limit_tpm: int = 60000  # tokens per minute

@dataclass
class ProviderStatus:
    """Status tracking for each provider"""
    is_available: bool = True
    last_error: Optional[str] = None
    error_count: int = 0
    last_request_time: float = 0
    requests_this_minute: int = 0
    tokens_this_minute: int = 0
    total_cost: float = 0
    reset_time: float = field(default_factory=time.time)

class LLMPool:
    """Multi-provider LLM pool with intelligent routing"""
    
    def __init__(self):
        self.providers = {}
        self.model_configs = {}
        self.provider_status = {}
        self.routing_rules = {}
        self.operation_mode = OperationMode.BALANCED
        self.fallback_chains = {}
        
        self._initialize_providers()
        self._setup_model_configs()
        self._setup_routing_rules()
        self._setup_fallback_chains()
    
    def _initialize_providers(self):
        """Initialize all LLM providers"""
        try:
            # OpenAI
            if os.getenv("OPENAI_API_KEY"):
                self.providers["openai"] = openai.AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
                self.provider_status["openai"] = ProviderStatus()
                logger.info("OpenAI provider initialized")
            
            # Anthropic
            if os.getenv("ANTHROPIC_API_KEY"):
                self.providers["anthropic"] = anthropic.AsyncAnthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))
                self.provider_status["anthropic"] = ProviderStatus()
                logger.info("Anthropic provider initialized")
            
            # Google
            if os.getenv("GOOGLE_AI_API_KEY"):
                genai.configure(api_key=os.getenv("GOOGLE_AI_API_KEY"))
                self.providers["google"] = genai
                self.provider_status["google"] = ProviderStatus()
                logger.info("Google provider initialized")
            
            # Groq
            if os.getenv("GROQ_API_KEY"):
                self.providers["groq"] = Groq(api_key=os.getenv("GROQ_API_KEY"))
                self.provider_status["groq"] = ProviderStatus()
                logger.info("Groq provider initialized")
            
            # OpenRouter (as fallback)
            if os.getenv("OPENROUTER_API_KEY"):
                self.providers["openrouter"] = openai.AsyncOpenAI(
                    base_url="https://openrouter.ai/api/v1",
                    api_key=os.getenv("OPENROUTER_API_KEY")
                )
                self.provider_status["openrouter"] = ProviderStatus()
                logger.info("OpenRouter provider initialized")
                
        except Exception as e:
            logger.error(f"Error initializing providers: {e}")
    
    def _setup_model_configs(self):
        """Setup model configurations"""
        self.model_configs = {
            # OpenAI Models
            "gpt-4o": ModelConfig("openai", "gpt-4o", 0.015, 128000, True, True, 7, 10, 500, 150000),
            "gpt-4o-mini": ModelConfig("openai", "gpt-4o-mini", 0.0015, 128000, True, True, 9, 8, 1000, 200000),
            "gpt-4": ModelConfig("openai", "gpt-4", 0.03, 8192, False, True, 5, 9, 200, 40000),
            
            # Anthropic Models
            "claude-3-5-sonnet": ModelConfig("anthropic", "claude-3-5-sonnet-20241022", 0.003, 200000, True, True, 6, 10, 300, 100000),
            "claude-3-haiku": ModelConfig("anthropic", "claude-3-haiku-20240307", 0.00025, 200000, True, True, 8, 7, 500, 150000),
            
            # Google Models
            "gemini-1.5-pro": ModelConfig("google", "gemini-1.5-pro", 0.0035, 2000000, True, True, 7, 9, 300, 120000),
            "gemini-1.5-flash": ModelConfig("google", "gemini-1.5-flash", 0.00035, 1000000, True, True, 10, 7, 1000, 300000),
            
            # Groq Models
            "llama-3.1-70b": ModelConfig("groq", "llama-3.1-70b-versatile", 0.00059, 131072, False, True, 10, 8, 30, 6000),
            "mixtral-8x7b": ModelConfig("groq", "mixtral-8x7b-32768", 0.00024, 32768, False, True, 10, 7, 30, 5000),
            
            # OpenRouter (Fallback)
            "openrouter-auto": ModelConfig("openrouter", "openrouter/auto", 0.002, 32768, False, True, 6, 7, 200, 50000),
        }
    
    def _setup_routing_rules(self):
        """Setup intelligent routing rules"""
        self.routing_rules = {
            TaskType.DOCUMENT_ANALYSIS: ["claude-3-5-sonnet", "gpt-4o", "gemini-1.5-pro"],
            TaskType.CODE_GENERATION: ["gpt-4o", "claude-3-5-sonnet", "gpt-4"],
            TaskType.QUICK_QUESTIONS: ["gemini-1.5-flash", "gpt-4o-mini", "claude-3-haiku"],
            TaskType.IMAGE_ANALYSIS: ["gpt-4o", "claude-3-5-sonnet", "gemini-1.5-pro"],
            TaskType.BACKGROUND_TASKS: ["gpt-4o-mini", "claude-3-haiku", "gemini-1.5-flash"],
            TaskType.COMPLEX_REASONING: ["gpt-4o", "claude-3-5-sonnet", "gpt-4"],
            TaskType.REAL_TIME_CHAT: ["llama-3.1-70b", "mixtral-8x7b", "gemini-1.5-flash"],
            TaskType.CREATIVE_WRITING: ["claude-3-5-sonnet", "gpt-4o", "gpt-4"],
            TaskType.DATA_EXTRACTION: ["gpt-4o-mini", "claude-3-haiku", "gemini-1.5-flash"],
            TaskType.SUMMARIZATION: ["claude-3-5-sonnet", "gpt-4o-mini", "gemini-1.5-flash"],
        }
    
    def _setup_fallback_chains(self):
        """Setup fallback chains for reliability"""
        self.fallback_chains = {
            "gpt-4o": ["claude-3-5-sonnet", "gemini-1.5-pro", "openrouter-auto"],
            "claude-3-5-sonnet": ["gpt-4o", "gemini-1.5-pro", "openrouter-auto"],
            "gemini-1.5-pro": ["gpt-4o", "claude-3-5-sonnet", "openrouter-auto"],
            "gpt-4o-mini": ["claude-3-haiku", "gemini-1.5-flash", "openrouter-auto"],
            "llama-3.1-70b": ["mixtral-8x7b", "gemini-1.5-flash", "gpt-4o-mini"],
        }
    
    def select_model(self, task_type: TaskType, requirements: Dict[str, Any] = None) -> str:
        """Select the best model for a given task"""
        requirements = requirements or {}
        
        # Get candidate models for this task type
        candidates = self.routing_rules.get(task_type, ["gpt-4o-mini"])
        
        # Filter based on requirements
        if requirements.get("supports_vision"):
            candidates = [m for m in candidates if self.model_configs[m].supports_vision]
        
        if requirements.get("max_cost_per_1k"):
            max_cost = requirements["max_cost_per_1k"]
            candidates = [m for m in candidates if self.model_configs[m].cost_per_1k_tokens <= max_cost]
        
        # Apply operation mode preferences
        if self.operation_mode == OperationMode.ECONOMY:
            candidates.sort(key=lambda m: self.model_configs[m].cost_per_1k_tokens)
        elif self.operation_mode == OperationMode.PERFORMANCE:
            candidates.sort(key=lambda m: -self.model_configs[m].quality_rating)
        else:  # BALANCED
            candidates.sort(key=lambda m: (
                self.model_configs[m].cost_per_1k_tokens * 0.3 +
                (10 - self.model_configs[m].quality_rating) * 0.4 +
                (10 - self.model_configs[m].speed_rating) * 0.3
            ))
        
        # Check availability and rate limits
        for model in candidates:
            config = self.model_configs[model]
            status = self.provider_status.get(config.provider)
            
            if status and status.is_available and self._check_rate_limits(config.provider):
                return model
        
        # Fallback to any available model
        logger.warning(f"No optimal model available for {task_type}, using fallback")
        return "openrouter-auto" if "openrouter" in self.providers else candidates[0]
    
    def _check_rate_limits(self, provider: str) -> bool:
        """Check if provider is within rate limits"""
        status = self.provider_status.get(provider)
        if not status:
            return False
        
        current_time = time.time()
        
        # Reset counters if a minute has passed
        if current_time - status.reset_time >= 60:
            status.requests_this_minute = 0
            status.tokens_this_minute = 0
            status.reset_time = current_time
        
        # Check rate limits (simplified - would need model-specific limits)
        return status.requests_this_minute < 50 and status.tokens_this_minute < 100000
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        task_type: TaskType = TaskType.REAL_TIME_CHAT,
        requirements: Dict[str, Any] = None,
        max_retries: int = 3
    ) -> Dict[str, Any]:
        """Generate response using the best available model"""
        
        model = self.select_model(task_type, requirements)
        config = self.model_configs[model]
        
        for attempt in range(max_retries):
            try:
                start_time = time.time()
                
                # Route to appropriate provider
                if config.provider == "openai":
                    response = await self._call_openai(model, messages, requirements)
                elif config.provider == "anthropic":
                    response = await self._call_anthropic(model, messages, requirements)
                elif config.provider == "google":
                    response = await self._call_google(model, messages, requirements)
                elif config.provider == "groq":
                    response = await self._call_groq(model, messages, requirements)
                elif config.provider == "openrouter":
                    response = await self._call_openrouter(model, messages, requirements)
                else:
                    raise ValueError(f"Unknown provider: {config.provider}")
                
                # Update usage statistics
                self._update_usage_stats(config.provider, response.get("usage", {}))
                
                response_time = time.time() - start_time
                
                return {
                    "content": response["content"],
                    "model": model,
                    "provider": config.provider,
                    "response_time": response_time,
                    "usage": response.get("usage", {}),
                    "cost": self._calculate_cost(config, response.get("usage", {}))
                }
                
            except Exception as e:
                logger.error(f"Error with {model} (attempt {attempt + 1}): {e}")
                
                # Mark provider as temporarily unavailable on repeated failures
                status = self.provider_status.get(config.provider)
                if status:
                    status.error_count += 1
                    status.last_error = str(e)
                    
                    if status.error_count >= 3:
                        status.is_available = False
                        logger.warning(f"Marking {config.provider} as unavailable")
                
                # Try fallback model
                if attempt < max_retries - 1:
                    fallback_models = self.fallback_chains.get(model, ["openrouter-auto"])
                    for fallback in fallback_models:
                        if fallback in self.model_configs:
                            fallback_config = self.model_configs[fallback]
                            fallback_status = self.provider_status.get(fallback_config.provider)
                            if fallback_status and fallback_status.is_available:
                                model = fallback
                                config = fallback_config
                                logger.info(f"Falling back to {model}")
                                break
        
        raise Exception(f"All models failed after {max_retries} attempts")
    
    async def _call_openai(self, model: str, messages: List[Dict], requirements: Dict = None) -> Dict:
        """Call OpenAI API"""
        client = self.providers["openai"]
        
        response = await client.chat.completions.create(
            model=model,
            messages=messages,
            max_tokens=requirements.get("max_tokens", 1000) if requirements else 1000,
            temperature=requirements.get("temperature", 0.7) if requirements else 0.7
        )
        
        return {
            "content": response.choices[0].message.content,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }
        }
    
    async def _call_anthropic(self, model: str, messages: List[Dict], requirements: Dict = None) -> Dict:
        """Call Anthropic API"""
        client = self.providers["anthropic"]
        
        # Convert messages format for Anthropic
        system_message = ""
        anthropic_messages = []
        
        for msg in messages:
            if msg["role"] == "system":
                system_message = msg["content"]
            else:
                anthropic_messages.append(msg)
        
        response = await client.messages.create(
            model=model,
            max_tokens=requirements.get("max_tokens", 1000) if requirements else 1000,
            system=system_message,
            messages=anthropic_messages
        )
        
        return {
            "content": response.content[0].text,
            "usage": {
                "prompt_tokens": response.usage.input_tokens,
                "completion_tokens": response.usage.output_tokens,
                "total_tokens": response.usage.input_tokens + response.usage.output_tokens
            }
        }
    
    async def _call_google(self, model: str, messages: List[Dict], requirements: Dict = None) -> Dict:
        """Call Google Gemini API"""
        # Simplified implementation - would need proper async handling
        model_instance = genai.GenerativeModel(model)
        
        # Convert messages to Google format
        prompt = "\n".join([f"{msg['role']}: {msg['content']}" for msg in messages])
        
        response = model_instance.generate_content(prompt)
        
        return {
            "content": response.text,
            "usage": {
                "prompt_tokens": 0,  # Google doesn't provide detailed usage
                "completion_tokens": 0,
                "total_tokens": 0
            }
        }
    
    async def _call_groq(self, model: str, messages: List[Dict], requirements: Dict = None) -> Dict:
        """Call Groq API"""
        client = self.providers["groq"]
        
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            max_tokens=requirements.get("max_tokens", 1000) if requirements else 1000,
            temperature=requirements.get("temperature", 0.7) if requirements else 0.7
        )
        
        return {
            "content": response.choices[0].message.content,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }
        }
    
    async def _call_openrouter(self, model: str, messages: List[Dict], requirements: Dict = None) -> Dict:
        """Call OpenRouter API"""
        client = self.providers["openrouter"]
        
        response = await client.chat.completions.create(
            model="openrouter/auto",  # Let OpenRouter choose
            messages=messages,
            max_tokens=requirements.get("max_tokens", 1000) if requirements else 1000,
            temperature=requirements.get("temperature", 0.7) if requirements else 0.7
        )
        
        return {
            "content": response.choices[0].message.content,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }
        }
    
    def _update_usage_stats(self, provider: str, usage: Dict):
        """Update usage statistics for provider"""
        status = self.provider_status.get(provider)
        if status and usage:
            status.requests_this_minute += 1
            status.tokens_this_minute += usage.get("total_tokens", 0)
            status.last_request_time = time.time()
    
    def _calculate_cost(self, config: ModelConfig, usage: Dict) -> float:
        """Calculate cost for the request"""
        if not usage:
            return 0.0
        
        total_tokens = usage.get("total_tokens", 0)
        return (total_tokens / 1000) * config.cost_per_1k_tokens
    
    def get_pool_status(self) -> Dict[str, Any]:
        """Get current pool status"""
        return {
            "providers": {
                provider: {
                    "available": status.is_available,
                    "error_count": status.error_count,
                    "last_error": status.last_error,
                    "requests_this_minute": status.requests_this_minute,
                    "tokens_this_minute": status.tokens_this_minute,
                    "total_cost": status.total_cost
                }
                for provider, status in self.provider_status.items()
            },
            "operation_mode": self.operation_mode.value,
            "total_providers": len(self.providers),
            "available_providers": sum(1 for s in self.provider_status.values() if s.is_available)
        }
    
    def set_operation_mode(self, mode: OperationMode):
        """Set operation mode"""
        self.operation_mode = mode
        logger.info(f"Operation mode set to {mode.value}")

# Global LLM pool instance
llm_pool = None

def initialize_llm_pool():
    """Initialize the global LLM pool"""
    global llm_pool
    llm_pool = LLMPool()
    logger.info("LLM Pool initialized successfully")

def get_llm_pool() -> LLMPool:
    """Get the global LLM pool instance"""
    if not llm_pool:
        initialize_llm_pool()
    return llm_pool
