#!/bin/bash

# MK13 MVP Celery Worker Startup Script

# Activate virtual environment
source venv/bin/activate

# Set environment variables
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Start Celery worker
echo "Starting Celery worker..."
celery -A celery_app worker --loglevel=info --concurrency=2 &

# Start Celery beat (scheduler)
echo "Starting Celery beat scheduler..."
celery -A celery_app beat --loglevel=info &

# Wait for both processes
wait
