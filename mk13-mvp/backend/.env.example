# MK13 MVP Backend Environment Configuration Template
# Copy this file to .env and fill in your actual values

# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=true

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_KEY=your_supabase_service_role_key_here

# Google Workspace Integration
GOOGLE_CLIENT_ID=your_google_client_id_here.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_REDIRECT_URI=http://localhost:8000/auth/google/callback

# LLM Provider Configuration
# OpenAI
OPENAI_API_KEY=sk-your_openai_api_key_here
OPENAI_MODEL=gpt-4o-mini

# Anthropic
ANTHROPIC_API_KEY=sk-ant-your_anthropic_api_key_here

# Google AI
GOOGLE_AI_API_KEY=your_google_ai_api_key_here

# Groq
GROQ_API_KEY=gsk_your_groq_api_key_here

# OpenRouter (Fallback)
OPENROUTER_API_KEY=sk-or-your_openrouter_api_key_here

# Redis Configuration (for Celery)
REDIS_URL=redis://localhost:6379/0

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Security Configuration
SECRET_KEY=your_secret_key_here_change_in_production_min_32_chars
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Encryption Configuration (AES-256 key - 32 bytes)
ENCRYPTION_KEY=your_32_byte_aes_256_encryption_key_here

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# Logging Configuration
LOG_LEVEL=INFO
