"""
Advanced Context Detection System for MK13 MVP
Automatic context detection based on app focus, calendar, email, and user patterns
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import re
from database import get_managers
from google_workspace import get_google_services

logger = logging.getLogger(__name__)

class ContextTrigger(Enum):
    """Types of context triggers"""
    APP_FOCUS = "app_focus"
    CALENDAR_EVENT = "calendar_event"
    EMAIL_RECEIVED = "email_received"
    TIME_BASED = "time_based"
    USER_PATTERN = "user_pattern"
    MANUAL = "manual"
    KEYWORD_DETECTED = "keyword_detected"

@dataclass
class ContextPattern:
    """Represents a learned context pattern"""
    pattern_id: str
    trigger_type: ContextTrigger
    conditions: Dict[str, Any]
    target_context: str
    confidence: float
    usage_count: int = 0
    success_rate: float = 1.0
    last_used: Optional[datetime] = None
    created_at: datetime = field(default_factory=datetime.now)

@dataclass
class DetectedContext:
    """Represents a detected context"""
    context_name: str
    confidence: float
    trigger: ContextTrigger
    trigger_data: Dict[str, Any]
    suggested_actions: List[str]
    timestamp: datetime = field(default_factory=datetime.now)

class ContextDetector:
    """Advanced context detection system"""
    
    def __init__(self):
        self.patterns = {}
        self.active_contexts = {}
        self.detection_rules = {}
        self.user_patterns = {}
        self.calendar_cache = {}
        self.email_cache = {}
        
        self._initialize_default_patterns()
        self._load_user_patterns()
    
    def _initialize_default_patterns(self):
        """Initialize default context detection patterns"""
        
        # Meeting preparation patterns
        self.patterns["meeting_prep"] = ContextPattern(
            pattern_id="meeting_prep",
            trigger_type=ContextTrigger.CALENDAR_EVENT,
            conditions={
                "time_before_meeting": 15,  # 15 minutes before
                "meeting_keywords": ["meeting", "call", "conference", "standup", "review"]
            },
            target_context="meeting_preparation",
            confidence=0.9
        )
        
        # Email response patterns
        self.patterns["email_response"] = ContextPattern(
            pattern_id="email_response",
            trigger_type=ContextTrigger.EMAIL_RECEIVED,
            conditions={
                "sender_importance": "high",
                "keywords": ["urgent", "asap", "deadline", "important"]
            },
            target_context="email_handling",
            confidence=0.8
        )
        
        # Work day start pattern
        self.patterns["work_start"] = ContextPattern(
            pattern_id="work_start",
            trigger_type=ContextTrigger.TIME_BASED,
            conditions={
                "time_range": ["08:00", "10:00"],
                "weekdays": [0, 1, 2, 3, 4]  # Monday to Friday
            },
            target_context="daily_planning",
            confidence=0.7
        )
        
        # Code development pattern
        self.patterns["code_dev"] = ContextPattern(
            pattern_id="code_dev",
            trigger_type=ContextTrigger.APP_FOCUS,
            conditions={
                "applications": ["code", "vscode", "pycharm", "intellij", "vim", "emacs"],
                "file_extensions": [".py", ".js", ".ts", ".java", ".cpp", ".go"]
            },
            target_context="development",
            confidence=0.85
        )
        
        # Document writing pattern
        self.patterns["doc_writing"] = ContextPattern(
            pattern_id="doc_writing",
            trigger_type=ContextTrigger.APP_FOCUS,
            conditions={
                "applications": ["word", "docs", "notion", "obsidian", "markdown"],
                "keywords": ["proposal", "report", "documentation", "spec"]
            },
            target_context="document_creation",
            confidence=0.8
        )
    
    async def detect_context(
        self, 
        trigger_type: ContextTrigger, 
        trigger_data: Dict[str, Any],
        user_id: str
    ) -> List[DetectedContext]:
        """Detect contexts based on trigger and data"""
        
        detected_contexts = []
        
        # Check all patterns for matches
        for pattern_id, pattern in self.patterns.items():
            if pattern.trigger_type == trigger_type:
                confidence = await self._evaluate_pattern(pattern, trigger_data, user_id)
                
                if confidence > 0.5:  # Threshold for context detection
                    suggested_actions = await self._generate_context_actions(
                        pattern.target_context, trigger_data, user_id
                    )
                    
                    detected_context = DetectedContext(
                        context_name=pattern.target_context,
                        confidence=confidence,
                        trigger=trigger_type,
                        trigger_data=trigger_data,
                        suggested_actions=suggested_actions
                    )
                    
                    detected_contexts.append(detected_context)
                    
                    # Update pattern usage
                    pattern.usage_count += 1
                    pattern.last_used = datetime.now()
        
        # Sort by confidence
        detected_contexts.sort(key=lambda x: x.confidence, reverse=True)
        
        return detected_contexts
    
    async def _evaluate_pattern(
        self, pattern: ContextPattern, trigger_data: Dict[str, Any], user_id: str
    ) -> float:
        """Evaluate how well trigger data matches a pattern"""
        
        base_confidence = pattern.confidence
        match_score = 0.0
        total_conditions = len(pattern.conditions)
        
        if total_conditions == 0:
            return 0.0
        
        for condition_key, condition_value in pattern.conditions.items():
            if condition_key == "time_before_meeting" and pattern.trigger_type == ContextTrigger.CALENDAR_EVENT:
                # Check if we're within the time window before a meeting
                meeting_time = trigger_data.get("meeting_time")
                if meeting_time:
                    time_diff = (meeting_time - datetime.now()).total_seconds() / 60
                    if 0 <= time_diff <= condition_value:
                        match_score += 1.0
            
            elif condition_key == "meeting_keywords":
                # Check if meeting title contains keywords
                meeting_title = trigger_data.get("title", "").lower()
                if any(keyword in meeting_title for keyword in condition_value):
                    match_score += 1.0
            
            elif condition_key == "sender_importance":
                # Check email sender importance
                sender = trigger_data.get("sender", "")
                importance = await self._get_sender_importance(sender, user_id)
                if importance == condition_value:
                    match_score += 1.0
            
            elif condition_key == "keywords":
                # Check for keywords in content
                content = trigger_data.get("content", "").lower()
                if any(keyword in content for keyword in condition_value):
                    match_score += 1.0
            
            elif condition_key == "time_range":
                # Check if current time is in range
                current_time = datetime.now().strftime("%H:%M")
                start_time, end_time = condition_value
                if start_time <= current_time <= end_time:
                    match_score += 1.0
            
            elif condition_key == "weekdays":
                # Check if current day is in weekdays
                current_weekday = datetime.now().weekday()
                if current_weekday in condition_value:
                    match_score += 1.0
            
            elif condition_key == "applications":
                # Check if focused application matches
                app_name = trigger_data.get("application", "").lower()
                if any(app in app_name for app in condition_value):
                    match_score += 1.0
            
            elif condition_key == "file_extensions":
                # Check if file extension matches
                file_path = trigger_data.get("file_path", "")
                if any(file_path.endswith(ext) for ext in condition_value):
                    match_score += 1.0
        
        # Calculate final confidence
        match_ratio = match_score / total_conditions
        final_confidence = base_confidence * match_ratio
        
        # Apply pattern success rate
        final_confidence *= pattern.success_rate
        
        return min(final_confidence, 1.0)
    
    async def _generate_context_actions(
        self, context_name: str, trigger_data: Dict[str, Any], user_id: str
    ) -> List[str]:
        """Generate suggested actions for a detected context"""
        
        actions = []
        
        if context_name == "meeting_preparation":
            meeting_title = trigger_data.get("title", "Unknown Meeting")
            actions = [
                f"Prepare agenda for '{meeting_title}'",
                "Review participant profiles",
                "Gather relevant documents",
                "Set up meeting notes template"
            ]
        
        elif context_name == "email_handling":
            sender = trigger_data.get("sender", "Unknown")
            actions = [
                f"Draft response to {sender}",
                "Check sender's recent communication",
                "Review related project context",
                "Set follow-up reminder"
            ]
        
        elif context_name == "daily_planning":
            actions = [
                "Review today's calendar",
                "Check priority tasks",
                "Review yesterday's progress",
                "Plan focus blocks"
            ]
        
        elif context_name == "development":
            file_path = trigger_data.get("file_path", "")
            actions = [
                f"Load project context for {file_path}",
                "Review recent commits",
                "Check related documentation",
                "Run tests and linting"
            ]
        
        elif context_name == "document_creation":
            actions = [
                "Load document templates",
                "Gather reference materials",
                "Check style guidelines",
                "Set writing goals"
            ]
        
        return actions
    
    async def _get_sender_importance(self, sender: str, user_id: str) -> str:
        """Determine sender importance based on communication history"""
        
        # TODO: Implement actual importance calculation
        # This would analyze:
        # - Frequency of communication
        # - Response time patterns
        # - Meeting attendance
        # - Organizational hierarchy
        
        # For now, simple heuristic
        if any(domain in sender for domain in ["@company.com", "@client.com"]):
            return "high"
        elif sender.endswith("@gmail.com"):
            return "medium"
        else:
            return "low"
    
    async def monitor_calendar_events(self, user_id: str):
        """Monitor upcoming calendar events for context triggers"""
        
        try:
            google_services = get_google_services()
            events = await google_services["calendar"].get_upcoming_events(user_id, max_results=10)
            
            for event in events:
                event_time = datetime.fromisoformat(event["start"].replace("Z", "+00:00"))
                time_until_event = (event_time - datetime.now()).total_seconds() / 60
                
                # Trigger context detection for upcoming meetings
                if 0 <= time_until_event <= 30:  # 30 minutes before
                    trigger_data = {
                        "title": event["summary"],
                        "meeting_time": event_time,
                        "participants": event.get("attendees", []),
                        "location": event.get("location", "")
                    }
                    
                    contexts = await self.detect_context(
                        ContextTrigger.CALENDAR_EVENT, trigger_data, user_id
                    )
                    
                    # Process detected contexts
                    for context in contexts:
                        await self._handle_detected_context(context, user_id)
        
        except Exception as e:
            logger.error(f"Error monitoring calendar events: {e}")
    
    async def monitor_email_activity(self, user_id: str):
        """Monitor email activity for context triggers"""
        
        try:
            google_services = get_google_services()
            emails = await google_services["gmail"].get_recent_emails(user_id, max_results=5)
            
            for email in emails:
                trigger_data = {
                    "sender": email["sender"],
                    "subject": email["subject"],
                    "content": email["snippet"],
                    "received_time": email["date"]
                }
                
                contexts = await self.detect_context(
                    ContextTrigger.EMAIL_RECEIVED, trigger_data, user_id
                )
                
                # Process detected contexts
                for context in contexts:
                    await self._handle_detected_context(context, user_id)
        
        except Exception as e:
            logger.error(f"Error monitoring email activity: {e}")
    
    async def _handle_detected_context(self, context: DetectedContext, user_id: str):
        """Handle a detected context by creating/updating user context"""
        
        try:
            managers = get_managers()
            
            # Check if context already exists
            existing_contexts = await managers["contexts"].get_user_contexts(user_id)
            existing_context = next(
                (ctx for ctx in existing_contexts if ctx["name"] == context.context_name), 
                None
            )
            
            if existing_context:
                # Update existing context
                updated_state = existing_context["state"].copy()
                updated_state.update({
                    "last_trigger": context.trigger.value,
                    "trigger_data": context.trigger_data,
                    "suggested_actions": context.suggested_actions,
                    "confidence": context.confidence,
                    "last_detection": context.timestamp.isoformat()
                })
                
                await managers["contexts"].update_context(
                    existing_context["id"], 
                    {"state": updated_state}
                )
            else:
                # Create new context
                context_state = {
                    "trigger": context.trigger.value,
                    "trigger_data": context.trigger_data,
                    "suggested_actions": context.suggested_actions,
                    "confidence": context.confidence,
                    "created_by": "auto_detection",
                    "last_detection": context.timestamp.isoformat()
                }
                
                await managers["contexts"].create_context(
                    user_id=user_id,
                    name=context.context_name,
                    state=context_state,
                    metadata={
                        "auto_created": True,
                        "detection_confidence": context.confidence,
                        "trigger_type": context.trigger.value
                    }
                )
            
            logger.info(f"Context '{context.context_name}' detected for user {user_id} with confidence {context.confidence}")
        
        except Exception as e:
            logger.error(f"Error handling detected context: {e}")
    
    def learn_pattern(
        self, 
        trigger_type: ContextTrigger, 
        trigger_data: Dict[str, Any], 
        target_context: str,
        user_feedback: bool
    ):
        """Learn new patterns from user behavior"""
        
        # TODO: Implement machine learning for pattern recognition
        # This would analyze successful context switches and create new patterns
        
        pattern_id = f"learned_{len(self.patterns)}"
        
        if user_feedback:  # Positive feedback
            # Create or strengthen pattern
            new_pattern = ContextPattern(
                pattern_id=pattern_id,
                trigger_type=trigger_type,
                conditions=self._extract_conditions(trigger_data),
                target_context=target_context,
                confidence=0.6  # Start with moderate confidence
            )
            
            self.patterns[pattern_id] = new_pattern
            logger.info(f"Learned new pattern: {pattern_id}")
        
        # Update existing pattern success rates based on feedback
        for pattern in self.patterns.values():
            if pattern.target_context == target_context:
                if user_feedback:
                    pattern.success_rate = min(pattern.success_rate + 0.1, 1.0)
                else:
                    pattern.success_rate = max(pattern.success_rate - 0.1, 0.1)
    
    def _extract_conditions(self, trigger_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract conditions from trigger data for pattern learning"""
        
        conditions = {}
        
        # Extract relevant conditions based on trigger data
        if "application" in trigger_data:
            conditions["applications"] = [trigger_data["application"].lower()]
        
        if "file_path" in trigger_data:
            file_ext = "." + trigger_data["file_path"].split(".")[-1]
            conditions["file_extensions"] = [file_ext]
        
        if "title" in trigger_data:
            # Extract keywords from title
            title_words = re.findall(r'\w+', trigger_data["title"].lower())
            conditions["keywords"] = title_words[:5]  # Top 5 words
        
        return conditions
    
    def _load_user_patterns(self):
        """Load user-specific patterns from storage"""
        # TODO: Implement persistent storage for user patterns
        pass
    
    def get_detection_stats(self) -> Dict[str, Any]:
        """Get context detection statistics"""
        
        return {
            "total_patterns": len(self.patterns),
            "active_contexts": len(self.active_contexts),
            "pattern_stats": {
                pattern_id: {
                    "usage_count": pattern.usage_count,
                    "success_rate": pattern.success_rate,
                    "confidence": pattern.confidence,
                    "last_used": pattern.last_used.isoformat() if pattern.last_used else None
                }
                for pattern_id, pattern in self.patterns.items()
            }
        }

# Global context detector instance
context_detector = None

def initialize_context_detector():
    """Initialize the global context detector"""
    global context_detector
    context_detector = ContextDetector()
    logger.info("Context Detector initialized successfully")

def get_context_detector() -> ContextDetector:
    """Get the global context detector instance"""
    if not context_detector:
        initialize_context_detector()
    return context_detector
