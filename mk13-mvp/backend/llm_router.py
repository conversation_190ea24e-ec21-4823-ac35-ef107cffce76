"""
LLM Router & Cost Optimizer for MK13 MVP
Advanced routing logic with cost optimization and performance monitoring
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
from llm_pool import LLMPool, TaskType, OperationMode, get_llm_pool

logger = logging.getLogger(__name__)

@dataclass
class RoutingDecision:
    """Represents a routing decision with reasoning"""
    selected_model: str
    provider: str
    confidence: float
    reasoning: str
    estimated_cost: float
    estimated_time: float
    fallback_models: List[str]

@dataclass
class CostBudget:
    """Cost budget configuration"""
    daily_limit: float = 50.0  # $50 per day default
    hourly_limit: float = 10.0  # $10 per hour default
    per_request_limit: float = 1.0  # $1 per request default
    current_daily_spend: float = 0.0
    current_hourly_spend: float = 0.0
    last_reset_time: datetime = None

class LLMRouter:
    """Advanced LLM routing with cost optimization"""
    
    def __init__(self, llm_pool: LLMPool):
        self.llm_pool = llm_pool
        self.cost_budget = CostBudget()
        self.performance_history = {}
        self.routing_cache = {}
        self.cache_ttl = 300  # 5 minutes
        
        # Load historical performance data
        self._load_performance_history()
    
    async def route_request(
        self,
        messages: List[Dict[str, str]],
        task_type: TaskType = TaskType.REAL_TIME_CHAT,
        requirements: Dict[str, Any] = None,
        user_preferences: Dict[str, Any] = None
    ) -> RoutingDecision:
        """Make intelligent routing decision"""
        
        requirements = requirements or {}
        user_preferences = user_preferences or {}
        
        # Check cost budget
        if not self._check_cost_budget(requirements.get("max_cost", 1.0)):
            return self._get_economy_routing(messages, task_type, requirements)
        
        # Analyze request complexity
        complexity_score = self._analyze_complexity(messages, task_type)
        
        # Get candidate models
        candidates = self._get_candidate_models(task_type, requirements, complexity_score)
        
        # Score and rank candidates
        scored_candidates = []
        for model in candidates:
            score = await self._score_model(model, messages, task_type, complexity_score, user_preferences)
            scored_candidates.append((model, score))
        
        # Sort by score (higher is better)
        scored_candidates.sort(key=lambda x: x[1]["total_score"], reverse=True)
        
        if not scored_candidates:
            raise Exception("No suitable models available")
        
        best_model, best_score = scored_candidates[0]
        config = self.llm_pool.model_configs[best_model]
        
        # Prepare fallback chain
        fallback_models = [model for model, _ in scored_candidates[1:4]]  # Top 3 alternatives
        
        return RoutingDecision(
            selected_model=best_model,
            provider=config.provider,
            confidence=best_score["confidence"],
            reasoning=best_score["reasoning"],
            estimated_cost=best_score["estimated_cost"],
            estimated_time=best_score["estimated_time"],
            fallback_models=fallback_models
        )
    
    def _analyze_complexity(self, messages: List[Dict], task_type: TaskType) -> float:
        """Analyze request complexity (0.0 to 1.0)"""
        complexity = 0.0
        
        # Message length factor
        total_chars = sum(len(msg.get("content", "")) for msg in messages)
        length_factor = min(total_chars / 10000, 1.0)  # Normalize to 10k chars
        complexity += length_factor * 0.3
        
        # Task type complexity
        task_complexity = {
            TaskType.QUICK_QUESTIONS: 0.1,
            TaskType.REAL_TIME_CHAT: 0.2,
            TaskType.SUMMARIZATION: 0.3,
            TaskType.DATA_EXTRACTION: 0.4,
            TaskType.CREATIVE_WRITING: 0.5,
            TaskType.CODE_GENERATION: 0.7,
            TaskType.DOCUMENT_ANALYSIS: 0.8,
            TaskType.COMPLEX_REASONING: 0.9,
            TaskType.IMAGE_ANALYSIS: 0.8,
        }
        complexity += task_complexity.get(task_type, 0.5) * 0.4
        
        # Context complexity (number of messages)
        context_factor = min(len(messages) / 20, 1.0)  # Normalize to 20 messages
        complexity += context_factor * 0.3
        
        return min(complexity, 1.0)
    
    def _get_candidate_models(
        self, 
        task_type: TaskType, 
        requirements: Dict[str, Any], 
        complexity_score: float
    ) -> List[str]:
        """Get candidate models based on task and requirements"""
        
        # Start with task-specific models
        candidates = self.llm_pool.routing_rules.get(task_type, ["gpt-4o-mini"])
        
        # Filter by requirements
        filtered_candidates = []
        for model in candidates:
            config = self.llm_pool.model_configs.get(model)
            if not config:
                continue
            
            # Check vision requirement
            if requirements.get("supports_vision") and not config.supports_vision:
                continue
            
            # Check function calling requirement
            if requirements.get("supports_functions") and not config.supports_function_calling:
                continue
            
            # Check cost requirement
            if requirements.get("max_cost_per_1k") and config.cost_per_1k_tokens > requirements["max_cost_per_1k"]:
                continue
            
            # Check provider availability
            provider_status = self.llm_pool.provider_status.get(config.provider)
            if not provider_status or not provider_status.is_available:
                continue
            
            filtered_candidates.append(model)
        
        # If complexity is high, prefer high-quality models
        if complexity_score > 0.7:
            filtered_candidates.sort(key=lambda m: self.llm_pool.model_configs[m].quality_rating, reverse=True)
        
        return filtered_candidates[:5]  # Limit to top 5 candidates
    
    async def _score_model(
        self,
        model: str,
        messages: List[Dict],
        task_type: TaskType,
        complexity_score: float,
        user_preferences: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Score a model for the given request"""
        
        config = self.llm_pool.model_configs[model]
        provider_status = self.llm_pool.provider_status[config.provider]
        
        # Base scores
        quality_score = config.quality_rating / 10.0
        speed_score = config.speed_rating / 10.0
        cost_score = 1.0 - (config.cost_per_1k_tokens / 0.1)  # Normalize to $0.1/1k tokens
        cost_score = max(0.0, min(1.0, cost_score))
        
        # Historical performance
        history_key = f"{model}_{task_type.value}"
        history = self.performance_history.get(history_key, {})
        reliability_score = 1.0 - (provider_status.error_count / 10.0)  # Penalize errors
        reliability_score = max(0.0, reliability_score)
        
        # Average response time from history
        avg_response_time = history.get("avg_response_time", 2.0)
        time_score = max(0.0, 1.0 - (avg_response_time / 10.0))  # Normalize to 10 seconds
        
        # User preferences
        preference_multiplier = 1.0
        preferred_providers = user_preferences.get("preferred_providers", [])
        if config.provider in preferred_providers:
            preference_multiplier = 1.2
        
        avoided_providers = user_preferences.get("avoided_providers", [])
        if config.provider in avoided_providers:
            preference_multiplier = 0.8
        
        # Operation mode weighting
        if self.llm_pool.operation_mode == OperationMode.ECONOMY:
            weights = {"cost": 0.5, "quality": 0.2, "speed": 0.1, "reliability": 0.2}
        elif self.llm_pool.operation_mode == OperationMode.PERFORMANCE:
            weights = {"cost": 0.1, "quality": 0.5, "speed": 0.2, "reliability": 0.2}
        else:  # BALANCED
            weights = {"cost": 0.25, "quality": 0.3, "speed": 0.25, "reliability": 0.2}
        
        # Calculate weighted score
        total_score = (
            quality_score * weights["quality"] +
            speed_score * weights["speed"] +
            cost_score * weights["cost"] +
            reliability_score * weights["reliability"]
        ) * preference_multiplier
        
        # Complexity adjustment
        if complexity_score > 0.7 and config.quality_rating < 8:
            total_score *= 0.8  # Penalize lower quality models for complex tasks
        
        # Estimate cost and time
        estimated_tokens = self._estimate_tokens(messages, task_type)
        estimated_cost = (estimated_tokens / 1000) * config.cost_per_1k_tokens
        estimated_time = avg_response_time
        
        # Generate reasoning
        reasoning = self._generate_reasoning(
            model, config, total_score, quality_score, cost_score, speed_score, reliability_score
        )
        
        return {
            "total_score": total_score,
            "quality_score": quality_score,
            "cost_score": cost_score,
            "speed_score": speed_score,
            "reliability_score": reliability_score,
            "estimated_cost": estimated_cost,
            "estimated_time": estimated_time,
            "confidence": min(total_score, 1.0),
            "reasoning": reasoning
        }
    
    def _estimate_tokens(self, messages: List[Dict], task_type: TaskType) -> int:
        """Estimate token usage for the request"""
        # Rough estimation based on character count
        total_chars = sum(len(msg.get("content", "")) for msg in messages)
        input_tokens = total_chars // 4  # Rough approximation
        
        # Estimate output tokens based on task type
        output_multipliers = {
            TaskType.QUICK_QUESTIONS: 0.2,
            TaskType.REAL_TIME_CHAT: 0.3,
            TaskType.SUMMARIZATION: 0.4,
            TaskType.DATA_EXTRACTION: 0.3,
            TaskType.CREATIVE_WRITING: 1.0,
            TaskType.CODE_GENERATION: 0.8,
            TaskType.DOCUMENT_ANALYSIS: 0.6,
            TaskType.COMPLEX_REASONING: 0.7,
        }
        
        output_tokens = input_tokens * output_multipliers.get(task_type, 0.5)
        return int(input_tokens + output_tokens)
    
    def _generate_reasoning(
        self, model: str, config, total_score: float, 
        quality_score: float, cost_score: float, speed_score: float, reliability_score: float
    ) -> str:
        """Generate human-readable reasoning for model selection"""
        
        reasons = []
        
        if quality_score > 0.8:
            reasons.append(f"High quality model ({config.quality_rating}/10)")
        if cost_score > 0.7:
            reasons.append(f"Cost-effective (${config.cost_per_1k_tokens:.4f}/1k tokens)")
        if speed_score > 0.8:
            reasons.append(f"Fast response ({config.speed_rating}/10 speed)")
        if reliability_score > 0.9:
            reasons.append("Highly reliable")
        
        if not reasons:
            reasons.append("Balanced choice for this task")
        
        return f"Selected {model}: " + ", ".join(reasons)
    
    def _check_cost_budget(self, estimated_cost: float) -> bool:
        """Check if request fits within cost budget"""
        now = datetime.now()
        
        # Reset daily budget if needed
        if (not self.cost_budget.last_reset_time or 
            now.date() > self.cost_budget.last_reset_time.date()):
            self.cost_budget.current_daily_spend = 0.0
            self.cost_budget.last_reset_time = now
        
        # Reset hourly budget if needed
        if (not self.cost_budget.last_reset_time or 
            now.hour != self.cost_budget.last_reset_time.hour):
            self.cost_budget.current_hourly_spend = 0.0
        
        # Check limits
        if (self.cost_budget.current_daily_spend + estimated_cost > self.cost_budget.daily_limit or
            self.cost_budget.current_hourly_spend + estimated_cost > self.cost_budget.hourly_limit or
            estimated_cost > self.cost_budget.per_request_limit):
            return False
        
        return True
    
    def _get_economy_routing(
        self, messages: List[Dict], task_type: TaskType, requirements: Dict[str, Any]
    ) -> RoutingDecision:
        """Get economy routing when budget is exceeded"""
        
        # Force economy mode
        original_mode = self.llm_pool.operation_mode
        self.llm_pool.operation_mode = OperationMode.ECONOMY
        
        # Get cheapest available model
        candidates = self._get_candidate_models(task_type, requirements, 0.1)
        if not candidates:
            candidates = ["gpt-4o-mini"]  # Fallback to cheapest OpenAI model
        
        model = candidates[0]
        config = self.llm_pool.model_configs[model]
        
        # Restore original mode
        self.llm_pool.operation_mode = original_mode
        
        return RoutingDecision(
            selected_model=model,
            provider=config.provider,
            confidence=0.5,
            reasoning=f"Budget exceeded, using economy model {model}",
            estimated_cost=0.001,  # Minimal cost
            estimated_time=3.0,
            fallback_models=[]
        )
    
    def update_performance_history(
        self, model: str, task_type: TaskType, response_time: float, 
        success: bool, cost: float
    ):
        """Update performance history for a model"""
        
        history_key = f"{model}_{task_type.value}"
        
        if history_key not in self.performance_history:
            self.performance_history[history_key] = {
                "total_requests": 0,
                "successful_requests": 0,
                "total_response_time": 0.0,
                "total_cost": 0.0,
                "avg_response_time": 0.0,
                "success_rate": 0.0,
                "avg_cost": 0.0
            }
        
        history = self.performance_history[history_key]
        history["total_requests"] += 1
        
        if success:
            history["successful_requests"] += 1
            history["total_response_time"] += response_time
            history["total_cost"] += cost
        
        # Update averages
        if history["successful_requests"] > 0:
            history["avg_response_time"] = history["total_response_time"] / history["successful_requests"]
            history["avg_cost"] = history["total_cost"] / history["successful_requests"]
        
        history["success_rate"] = history["successful_requests"] / history["total_requests"]
        
        # Update cost budget
        self.cost_budget.current_daily_spend += cost
        self.cost_budget.current_hourly_spend += cost
    
    def _load_performance_history(self):
        """Load performance history from storage"""
        # TODO: Implement persistent storage
        self.performance_history = {}
    
    def get_routing_stats(self) -> Dict[str, Any]:
        """Get routing statistics"""
        return {
            "cost_budget": {
                "daily_limit": self.cost_budget.daily_limit,
                "daily_spent": self.cost_budget.current_daily_spend,
                "daily_remaining": self.cost_budget.daily_limit - self.cost_budget.current_daily_spend,
                "hourly_limit": self.cost_budget.hourly_limit,
                "hourly_spent": self.cost_budget.current_hourly_spend,
            },
            "performance_history": self.performance_history,
            "cache_size": len(self.routing_cache)
        }

# Global router instance
llm_router = None

def initialize_llm_router():
    """Initialize the global LLM router"""
    global llm_router
    llm_pool = get_llm_pool()
    llm_router = LLMRouter(llm_pool)
    logger.info("LLM Router initialized successfully")

def get_llm_router() -> LLMRouter:
    """Get the global LLM router instance"""
    if not llm_router:
        initialize_llm_router()
    return llm_router
