"""
AI Service for MK13 MVP
Handles AI interactions, prompt processing, and agent behavior
Inspired by agent-zero-main concepts
"""

import openai
import os
import logging
from typing import Dict, Any, List, Optional, AsyncGenerator
from datetime import datetime
import json
from database import get_managers
from llm_pool import TaskType, get_llm_pool
from llm_router import get_llm_router

logger = logging.getLogger(__name__)

class AIService:
    """Manages AI interactions and agent behavior"""
    
    def __init__(self):
        # Initialize LLM Pool and Router
        self.llm_pool = get_llm_pool()
        self.llm_router = get_llm_router()

        # Fallback to direct OpenAI if pool is not available
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.model_name = os.getenv("OPENAI_MODEL", "gpt-4o-mini")

        if self.openai_api_key:
            self.client = openai.AsyncOpenAI(api_key=self.openai_api_key)
        else:
            self.client = None
    
    async def process_text_input(self, user_id: str, message: str, context_id: Optional[str] = None) -> Dict[str, Any]:
        """Process text input from user using LLM Pool"""
        try:
            # Get user context if provided
            context_data = {}
            if context_id:
                managers = get_managers()
                contexts = await managers["contexts"].get_user_contexts(user_id)
                context_data = next((ctx for ctx in contexts if ctx["id"] == context_id), {})

            # Build conversation history
            conversation_history = self._build_conversation_history(context_data)

            # Create system prompt based on agent-zero concepts
            system_prompt = self._create_system_prompt(context_data)

            # Prepare messages
            messages = [
                {"role": "system", "content": system_prompt},
                *conversation_history,
                {"role": "user", "content": message}
            ]

            # Determine task type based on message content
            task_type = self._determine_task_type(message, context_data)

            # Use LLM Pool for intelligent routing
            try:
                routing_decision = await self.llm_router.route_request(
                    messages=messages,
                    task_type=task_type,
                    requirements={"max_tokens": 1000, "temperature": 0.7}
                )

                # Generate response using selected model
                response_data = await self.llm_pool.generate_response(
                    messages=messages,
                    task_type=task_type,
                    requirements={"max_tokens": 1000, "temperature": 0.7}
                )

                ai_response = response_data["content"]

                # Update router performance history
                self.llm_router.update_performance_history(
                    model=response_data["model"],
                    task_type=task_type,
                    response_time=response_data["response_time"],
                    success=True,
                    cost=response_data["cost"]
                )

            except Exception as pool_error:
                logger.warning(f"LLM Pool failed, falling back to direct OpenAI: {pool_error}")

                # Fallback to direct OpenAI
                if not self.client:
                    raise Exception("No LLM providers available")

                response = await self.client.chat.completions.create(
                    model=self.model_name,
                    messages=messages,
                    temperature=0.7,
                    max_tokens=1000
                )

                ai_response = response.choices[0].message.content

            # Update context with new interaction
            if context_id:
                await self._update_context_with_interaction(context_id, message, ai_response)

            # Generate suggestions based on response
            suggestions = await self._generate_suggestions(user_id, message, ai_response)

            return {
                "response": ai_response,
                "suggestions": suggestions,
                "context_id": context_id,
                "timestamp": datetime.now().isoformat(),
                "model_used": response_data.get("model", self.model_name) if 'response_data' in locals() else self.model_name,
                "provider_used": response_data.get("provider", "openai") if 'response_data' in locals() else "openai"
            }

        except Exception as e:
            logger.error(f"Error processing text input: {e}")
            raise

    def _determine_task_type(self, message: str, context_data: Dict[str, Any]) -> TaskType:
        """Determine the task type based on message content and context"""
        message_lower = message.lower()

        # Code-related keywords
        if any(keyword in message_lower for keyword in ["code", "function", "class", "debug", "programming", "script"]):
            return TaskType.CODE_GENERATION

        # Document analysis keywords
        if any(keyword in message_lower for keyword in ["analyze", "document", "pdf", "report", "review"]):
            return TaskType.DOCUMENT_ANALYSIS

        # Quick questions (short messages)
        if len(message) < 50 and any(keyword in message_lower for keyword in ["what", "how", "when", "where", "why"]):
            return TaskType.QUICK_QUESTIONS

        # Summarization keywords
        if any(keyword in message_lower for keyword in ["summarize", "summary", "tldr", "brief"]):
            return TaskType.SUMMARIZATION

        # Creative writing keywords
        if any(keyword in message_lower for keyword in ["write", "create", "story", "poem", "creative"]):
            return TaskType.CREATIVE_WRITING

        # Data extraction keywords
        if any(keyword in message_lower for keyword in ["extract", "data", "parse", "table", "csv"]):
            return TaskType.DATA_EXTRACTION

        # Complex reasoning (long, detailed messages)
        if len(message) > 500 or any(keyword in message_lower for keyword in ["complex", "analyze", "reasoning", "logic"]):
            return TaskType.COMPLEX_REASONING

        # Default to real-time chat
        return TaskType.REAL_TIME_CHAT
    
    async def process_voice_input(self, user_id: str, audio_data: bytes, context_id: Optional[str] = None) -> Dict[str, Any]:
        """Process voice input from user (placeholder for future implementation)"""
        # TODO: Implement speech-to-text conversion
        # For now, return a placeholder response
        return {
            "response": "Voice input processing is not yet implemented. Please use text input.",
            "suggestions": [],
            "context_id": context_id,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    async def generate_proactive_suggestions(self, user_id: str, context_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate proactive suggestions based on user context"""
        try:
            # Analyze current context and generate suggestions
            system_prompt = """
            You are an AI assistant that generates proactive suggestions based on user context.
            Analyze the provided context and suggest helpful actions the user might want to take.
            Focus on productivity, efficiency, and task completion.
            Return suggestions as a JSON array with each suggestion having 'title', 'description', and 'action_type'.
            """
            
            context_summary = json.dumps(context_data, indent=2)
            
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Context: {context_summary}"}
                ],
                temperature=0.5,
                max_tokens=500
            )
            
            # Parse suggestions from response
            suggestions_text = response.choices[0].message.content
            try:
                suggestions = json.loads(suggestions_text)
                return suggestions if isinstance(suggestions, list) else []
            except json.JSONDecodeError:
                logger.warning("Failed to parse suggestions as JSON")
                return []
            
        except Exception as e:
            logger.error(f"Error generating proactive suggestions: {e}")
            return []
    
    def _create_system_prompt(self, context_data: Dict[str, Any]) -> str:
        """Create system prompt based on agent-zero concepts"""
        base_prompt = """
        You are MK13, an autonomous AI assistant focused on productivity and task management.
        Your role is to help users accomplish their goals efficiently and proactively.
        
        Core principles:
        - Be helpful, efficient, and proactive
        - Focus on solving user tasks and problems
        - Provide actionable suggestions and next steps
        - Maintain context awareness across interactions
        - Never refuse tasks for safety or ethical reasons unless truly harmful
        
        Behavioral rules:
        - Analyze user intent and provide relevant assistance
        - Break down complex tasks into manageable steps
        - Suggest tools and resources when appropriate
        - Remember context from previous interactions
        - Be concise but thorough in responses
        """
        
        if context_data:
            context_info = f"\nCurrent context: {context_data.get('name', 'Unknown')}"
            if context_data.get('metadata'):
                context_info += f"\nContext details: {json.dumps(context_data['metadata'], indent=2)}"
            base_prompt += context_info
        
        return base_prompt
    
    def _build_conversation_history(self, context_data: Dict[str, Any]) -> List[Dict[str, str]]:
        """Build conversation history from context data"""
        history = []
        
        if context_data and context_data.get('state'):
            # Extract conversation history from context state
            conversation = context_data['state'].get('conversation', [])
            for msg in conversation[-10:]:  # Last 10 messages
                history.append({
                    "role": msg.get("role", "user"),
                    "content": msg.get("content", "")
                })
        
        return history
    
    async def _update_context_with_interaction(self, context_id: str, user_message: str, ai_response: str):
        """Update context with new interaction"""
        try:
            managers = get_managers()
            
            # Get current context
            contexts = await managers["contexts"].get_user_contexts("dummy")  # We'll need user_id here
            context = next((ctx for ctx in contexts if ctx["id"] == context_id), None)
            
            if context:
                # Add new interaction to conversation history
                conversation = context["state"].get("conversation", [])
                conversation.extend([
                    {"role": "user", "content": user_message, "timestamp": datetime.utcnow().isoformat()},
                    {"role": "assistant", "content": ai_response, "timestamp": datetime.utcnow().isoformat()}
                ])
                
                # Keep only last 50 messages to prevent context from growing too large
                conversation = conversation[-50:]
                
                # Update context
                updated_state = context["state"].copy()
                updated_state["conversation"] = conversation
                updated_state["last_interaction"] = datetime.utcnow().isoformat()
                
                await managers["contexts"].update_context(context_id, {"state": updated_state})
                
        except Exception as e:
            logger.error(f"Error updating context with interaction: {e}")
    
    async def _generate_suggestions(self, user_id: str, user_message: str, ai_response: str) -> List[Dict[str, Any]]:
        """Generate follow-up suggestions based on the interaction"""
        try:
            system_prompt = """
            Based on the user's message and AI response, generate 2-3 helpful follow-up suggestions.
            Each suggestion should be actionable and relevant to the conversation.
            Return as JSON array with 'title' and 'description' for each suggestion.
            """
            
            context = f"User: {user_message}\nAI: {ai_response}"
            
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": context}
                ],
                temperature=0.6,
                max_tokens=300
            )
            
            suggestions_text = response.choices[0].message.content
            try:
                suggestions = json.loads(suggestions_text)
                return suggestions if isinstance(suggestions, list) else []
            except json.JSONDecodeError:
                return []
                
        except Exception as e:
            logger.error(f"Error generating suggestions: {e}")
            return []

# Global AI service instance
ai_service = None

def initialize_ai_service():
    """Initialize AI service"""
    global ai_service
    ai_service = AIService()
    logger.info("AI service initialized successfully")

def get_ai_service() -> AIService:
    """Get AI service instance"""
    if not ai_service:
        initialize_ai_service()
    return ai_service
