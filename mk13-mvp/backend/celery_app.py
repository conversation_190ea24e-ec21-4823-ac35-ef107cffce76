"""
Celery configuration for MK13 MVP
Handles background tasks like email monitoring and asynchronous operations
"""

from celery import Celery
import os
from dotenv import load_dotenv
import logging
from typing import Dict, Any, List
from database import get_managers
from google_workspace import get_google_services
from ai_service import get_ai_service
import asyncio
from datetime import datetime, timedelta
import json

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

# Create Celery app
celery_app = Celery(
    'mk13_mvp',
    broker=os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0'),
    backend=os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0'),
    include=['celery_app']
)

# Celery configuration
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# Periodic task schedule
celery_app.conf.beat_schedule = {
    'monitor-emails': {
        'task': 'celery_app.monitor_user_emails',
        'schedule': 300.0,  # Every 5 minutes
    },
    'generate-proactive-suggestions': {
        'task': 'celery_app.generate_proactive_suggestions_for_users',
        'schedule': 900.0,  # Every 15 minutes
    },
    'cleanup-old-contexts': {
        'task': 'celery_app.cleanup_old_contexts',
        'schedule': 3600.0,  # Every hour
    },
}

@celery_app.task(bind=True)
def monitor_user_emails(self):
    """Monitor emails for all users and generate suggestions"""
    try:
        logger.info("Starting email monitoring task")
        
        # Run async function in sync context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(_monitor_emails_async())
        loop.close()
        
        logger.info(f"Email monitoring completed: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Error in email monitoring task: {e}")
        self.retry(countdown=60, max_retries=3)

async def _monitor_emails_async():
    """Async implementation of email monitoring"""
    try:
        managers = get_managers()
        google_services = get_google_services()
        ai_service = get_ai_service()
        
        # Get all users (simplified - in production, get users with Google tokens)
        # For now, we'll use a placeholder approach
        processed_users = 0
        suggestions_generated = 0
        
        # TODO: Implement actual user iteration when user management is complete
        # users = await managers["users"].get_all_users_with_google_tokens()
        
        # Placeholder for demonstration
        logger.info("Email monitoring would process users here")
        
        return {
            "processed_users": processed_users,
            "suggestions_generated": suggestions_generated,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in async email monitoring: {e}")
        raise

@celery_app.task(bind=True)
def process_email_suggestions(self, user_id: str, emails: List[Dict[str, Any]]):
    """Process emails and generate AI suggestions"""
    try:
        logger.info(f"Processing email suggestions for user {user_id}")
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(_process_email_suggestions_async(user_id, emails))
        loop.close()
        
        return result
        
    except Exception as e:
        logger.error(f"Error processing email suggestions: {e}")
        self.retry(countdown=30, max_retries=2)

async def _process_email_suggestions_async(user_id: str, emails: List[Dict[str, Any]]):
    """Async implementation of email suggestion processing"""
    try:
        ai_service = get_ai_service()
        managers = get_managers()
        
        suggestions = []
        
        for email in emails:
            # Generate AI suggestions based on email content
            email_context = {
                "subject": email.get("subject", ""),
                "sender": email.get("sender", ""),
                "snippet": email.get("snippet", "")
            }
            
            # Use AI to generate suggestions
            ai_suggestions = await ai_service.generate_proactive_suggestions(user_id, email_context)
            
            for suggestion in ai_suggestions:
                suggestion["source"] = "email"
                suggestion["email_id"] = email.get("id")
                suggestions.append(suggestion)
        
        # Store suggestions in context or feedback system
        if suggestions:
            # Create a context for email suggestions
            await managers["contexts"].create_context(
                user_id=user_id,
                name=f"Email Suggestions {datetime.utcnow().strftime('%Y-%m-%d %H:%M')}",
                state={"suggestions": suggestions, "type": "email_suggestions"},
                metadata={"generated_at": datetime.utcnow().isoformat(), "email_count": len(emails)}
            )
        
        return {
            "user_id": user_id,
            "suggestions_count": len(suggestions),
            "processed_emails": len(emails)
        }
        
    except Exception as e:
        logger.error(f"Error in async email suggestion processing: {e}")
        raise

@celery_app.task(bind=True)
def generate_proactive_suggestions_for_users(self):
    """Generate proactive suggestions for all active users"""
    try:
        logger.info("Starting proactive suggestions generation")
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(_generate_proactive_suggestions_async())
        loop.close()
        
        return result
        
    except Exception as e:
        logger.error(f"Error generating proactive suggestions: {e}")
        self.retry(countdown=120, max_retries=2)

async def _generate_proactive_suggestions_async():
    """Async implementation of proactive suggestions generation"""
    try:
        managers = get_managers()
        ai_service = get_ai_service()
        google_services = get_google_services()
        
        processed_users = 0
        total_suggestions = 0
        
        # TODO: Get active users from database
        # For now, placeholder implementation
        logger.info("Proactive suggestions would be generated for active users here")
        
        return {
            "processed_users": processed_users,
            "total_suggestions": total_suggestions,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in async proactive suggestions: {e}")
        raise

@celery_app.task(bind=True)
def cleanup_old_contexts(self):
    """Clean up old contexts and data"""
    try:
        logger.info("Starting context cleanup task")
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(_cleanup_old_contexts_async())
        loop.close()
        
        return result
        
    except Exception as e:
        logger.error(f"Error in cleanup task: {e}")
        self.retry(countdown=300, max_retries=1)

async def _cleanup_old_contexts_async():
    """Async implementation of context cleanup"""
    try:
        managers = get_managers()
        
        # Define cleanup criteria (e.g., contexts older than 30 days)
        cutoff_date = datetime.utcnow() - timedelta(days=30)
        
        # TODO: Implement actual cleanup logic
        # This would involve querying old contexts and removing them
        cleaned_contexts = 0
        
        logger.info(f"Context cleanup completed: {cleaned_contexts} contexts cleaned")
        
        return {
            "cleaned_contexts": cleaned_contexts,
            "cutoff_date": cutoff_date.isoformat(),
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in async context cleanup: {e}")
        raise

@celery_app.task(bind=True)
def process_ai_request(self, user_id: str, message: str, context_id: str = None):
    """Process AI request asynchronously"""
    try:
        logger.info(f"Processing AI request for user {user_id}")
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(_process_ai_request_async(user_id, message, context_id))
        loop.close()
        
        return result
        
    except Exception as e:
        logger.error(f"Error processing AI request: {e}")
        self.retry(countdown=10, max_retries=2)

async def _process_ai_request_async(user_id: str, message: str, context_id: str = None):
    """Async implementation of AI request processing"""
    try:
        ai_service = get_ai_service()
        
        result = await ai_service.process_text_input(user_id, message, context_id)
        
        return {
            "user_id": user_id,
            "response": result["response"],
            "suggestions": result["suggestions"],
            "context_id": result["context_id"],
            "processed_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in async AI request processing: {e}")
        raise

# Utility functions for task management
def start_email_monitoring_for_user(user_id: str):
    """Start email monitoring for a specific user"""
    return monitor_user_emails.delay()

def queue_ai_request(user_id: str, message: str, context_id: str = None):
    """Queue an AI request for processing"""
    return process_ai_request.delay(user_id, message, context_id)

def get_task_status(task_id: str):
    """Get status of a Celery task"""
    result = celery_app.AsyncResult(task_id)
    return {
        "task_id": task_id,
        "status": result.status,
        "result": result.result if result.ready() else None,
        "traceback": result.traceback if result.failed() else None
    }

if __name__ == '__main__':
    # For running Celery worker
    celery_app.start()
