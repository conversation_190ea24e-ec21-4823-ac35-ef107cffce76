"""
Google Workspace Integration for MK13 MVP
Handles OAuth2 authentication and interactions with Gmail, Calendar, and Drive
"""

import os
import logging
from typing import Dict, Any, List, Optional
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import json
from datetime import datetime, timedelta
from database import get_managers

logger = logging.getLogger(__name__)

class GoogleWorkspaceService:
    """Manages Google Workspace integrations"""
    
    # OAuth 2.0 scopes
    SCOPES = [
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/gmail.send',
        'https://www.googleapis.com/auth/calendar.readonly',
        'https://www.googleapis.com/auth/calendar.events',
        'https://www.googleapis.com/auth/drive.readonly',
        'https://www.googleapis.com/auth/drive.file'
    ]
    
    def __init__(self):
        self.client_id = os.getenv("GOOGLE_CLIENT_ID")
        self.client_secret = os.getenv("GOOGLE_CLIENT_SECRET")
        self.redirect_uri = os.getenv("GOOGLE_REDIRECT_URI", "http://localhost:8000/auth/google/callback")
        
        if not all([self.client_id, self.client_secret]):
            raise ValueError("Missing Google OAuth credentials")
        
        self.client_config = {
            "web": {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "redirect_uris": [self.redirect_uri]
            }
        }
    
    def get_authorization_url(self, user_id: str) -> str:
        """Get OAuth2 authorization URL"""
        try:
            flow = Flow.from_client_config(
                self.client_config,
                scopes=self.SCOPES,
                redirect_uri=self.redirect_uri
            )
            
            auth_url, _ = flow.authorization_url(
                access_type='offline',
                include_granted_scopes='true',
                state=user_id  # Pass user_id as state parameter
            )
            
            return auth_url
            
        except Exception as e:
            logger.error(f"Error getting authorization URL: {e}")
            raise
    
    async def handle_oauth_callback(self, code: str, state: str) -> Dict[str, Any]:
        """Handle OAuth2 callback and store credentials"""
        try:
            user_id = state  # user_id was passed as state
            
            flow = Flow.from_client_config(
                self.client_config,
                scopes=self.SCOPES,
                redirect_uri=self.redirect_uri
            )
            
            flow.fetch_token(code=code)
            credentials = flow.credentials
            
            # Store credentials for user
            await self._store_user_credentials(user_id, credentials)
            
            return {
                "user_id": user_id,
                "access_token": credentials.token,
                "refresh_token": credentials.refresh_token,
                "expires_at": credentials.expiry.isoformat() if credentials.expiry else None
            }
            
        except Exception as e:
            logger.error(f"Error handling OAuth callback: {e}")
            raise
    
    async def _store_user_credentials(self, user_id: str, credentials: Credentials):
        """Store user credentials in database"""
        try:
            managers = get_managers()
            
            # Serialize credentials
            creds_data = {
                "token": credentials.token,
                "refresh_token": credentials.refresh_token,
                "token_uri": credentials.token_uri,
                "client_id": credentials.client_id,
                "client_secret": credentials.client_secret,
                "scopes": credentials.scopes,
                "expiry": credentials.expiry.isoformat() if credentials.expiry else None
            }
            
            # Update user with Google token
            await managers["users"].update_user(user_id, {
                "google_token": json.dumps(creds_data)
            })
            
        except Exception as e:
            logger.error(f"Error storing user credentials: {e}")
            raise
    
    async def _get_user_credentials(self, user_id: str) -> Optional[Credentials]:
        """Get user credentials from database"""
        try:
            managers = get_managers()
            user = await managers["users"].get_user_by_email("<EMAIL>")  # We need to get by user_id
            
            if not user or not user.get("google_token"):
                return None
            
            creds_data = json.loads(user["google_token"])
            
            credentials = Credentials(
                token=creds_data["token"],
                refresh_token=creds_data["refresh_token"],
                token_uri=creds_data["token_uri"],
                client_id=creds_data["client_id"],
                client_secret=creds_data["client_secret"],
                scopes=creds_data["scopes"]
            )
            
            # Set expiry if available
            if creds_data.get("expiry"):
                credentials.expiry = datetime.fromisoformat(creds_data["expiry"])
            
            # Refresh if expired
            if credentials.expired and credentials.refresh_token:
                credentials.refresh(Request())
                await self._store_user_credentials(user_id, credentials)
            
            return credentials
            
        except Exception as e:
            logger.error(f"Error getting user credentials: {e}")
            return None

class GmailService:
    """Gmail integration service"""
    
    def __init__(self, workspace_service: GoogleWorkspaceService):
        self.workspace_service = workspace_service
    
    async def get_recent_emails(self, user_id: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """Get recent emails for user"""
        try:
            credentials = await self.workspace_service._get_user_credentials(user_id)
            if not credentials:
                raise Exception("No valid credentials found")
            
            service = build('gmail', 'v1', credentials=credentials)
            
            # Get message list
            results = service.users().messages().list(
                userId='me',
                maxResults=max_results,
                q='is:unread'  # Only unread emails
            ).execute()
            
            messages = results.get('messages', [])
            emails = []
            
            for message in messages:
                # Get full message details
                msg = service.users().messages().get(
                    userId='me',
                    id=message['id']
                ).execute()
                
                # Extract email data
                headers = msg['payload'].get('headers', [])
                subject = next((h['value'] for h in headers if h['name'] == 'Subject'), 'No Subject')
                sender = next((h['value'] for h in headers if h['name'] == 'From'), 'Unknown Sender')
                date = next((h['value'] for h in headers if h['name'] == 'Date'), '')
                
                emails.append({
                    'id': message['id'],
                    'subject': subject,
                    'sender': sender,
                    'date': date,
                    'snippet': msg.get('snippet', '')
                })
            
            return emails
            
        except HttpError as e:
            logger.error(f"Gmail API error: {e}")
            raise
        except Exception as e:
            logger.error(f"Error getting recent emails: {e}")
            raise
    
    async def send_email(self, user_id: str, to: str, subject: str, body: str) -> Dict[str, Any]:
        """Send email"""
        try:
            credentials = await self.workspace_service._get_user_credentials(user_id)
            if not credentials:
                raise Exception("No valid credentials found")
            
            service = build('gmail', 'v1', credentials=credentials)
            
            # Create message
            message = {
                'raw': self._create_message(to, subject, body)
            }
            
            # Send message
            result = service.users().messages().send(
                userId='me',
                body=message
            ).execute()
            
            return {
                'message_id': result['id'],
                'status': 'sent'
            }
            
        except Exception as e:
            logger.error(f"Error sending email: {e}")
            raise
    
    def _create_message(self, to: str, subject: str, body: str) -> str:
        """Create email message in RFC 2822 format"""
        import base64
        from email.mime.text import MIMEText
        
        message = MIMEText(body)
        message['to'] = to
        message['subject'] = subject
        
        return base64.urlsafe_b64encode(message.as_bytes()).decode()

class CalendarService:
    """Google Calendar integration service"""
    
    def __init__(self, workspace_service: GoogleWorkspaceService):
        self.workspace_service = workspace_service
    
    async def get_upcoming_events(self, user_id: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """Get upcoming calendar events"""
        try:
            credentials = await self.workspace_service._get_user_credentials(user_id)
            if not credentials:
                raise Exception("No valid credentials found")
            
            service = build('calendar', 'v3', credentials=credentials)
            
            # Get events from now
            now = datetime.utcnow().isoformat() + 'Z'
            
            events_result = service.events().list(
                calendarId='primary',
                timeMin=now,
                maxResults=max_results,
                singleEvents=True,
                orderBy='startTime'
            ).execute()
            
            events = events_result.get('items', [])
            
            formatted_events = []
            for event in events:
                start = event['start'].get('dateTime', event['start'].get('date'))
                end = event['end'].get('dateTime', event['end'].get('date'))
                
                formatted_events.append({
                    'id': event['id'],
                    'summary': event.get('summary', 'No Title'),
                    'start': start,
                    'end': end,
                    'description': event.get('description', ''),
                    'location': event.get('location', '')
                })
            
            return formatted_events
            
        except Exception as e:
            logger.error(f"Error getting calendar events: {e}")
            raise

class DriveService:
    """Google Drive integration service"""
    
    def __init__(self, workspace_service: GoogleWorkspaceService):
        self.workspace_service = workspace_service
    
    async def get_recent_files(self, user_id: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """Get recent files from Google Drive"""
        try:
            credentials = await self.workspace_service._get_user_credentials(user_id)
            if not credentials:
                raise Exception("No valid credentials found")
            
            service = build('drive', 'v3', credentials=credentials)
            
            # Get recent files
            results = service.files().list(
                pageSize=max_results,
                orderBy='modifiedTime desc',
                fields="files(id,name,mimeType,modifiedTime,webViewLink)"
            ).execute()
            
            files = results.get('files', [])
            
            formatted_files = []
            for file in files:
                formatted_files.append({
                    'id': file['id'],
                    'name': file['name'],
                    'type': file['mimeType'],
                    'modified': file['modifiedTime'],
                    'link': file['webViewLink']
                })
            
            return formatted_files
            
        except Exception as e:
            logger.error(f"Error getting Drive files: {e}")
            raise

# Global service instances
google_workspace_service = None
gmail_service = None
calendar_service = None
drive_service = None

def initialize_google_services():
    """Initialize Google Workspace services"""
    global google_workspace_service, gmail_service, calendar_service, drive_service
    
    google_workspace_service = GoogleWorkspaceService()
    gmail_service = GmailService(google_workspace_service)
    calendar_service = CalendarService(google_workspace_service)
    drive_service = DriveService(google_workspace_service)
    
    logger.info("Google Workspace services initialized successfully")

def get_google_services():
    """Get Google service instances"""
    if not google_workspace_service:
        initialize_google_services()
    
    return {
        "workspace": google_workspace_service,
        "gmail": gmail_service,
        "calendar": calendar_service,
        "drive": drive_service
    }
